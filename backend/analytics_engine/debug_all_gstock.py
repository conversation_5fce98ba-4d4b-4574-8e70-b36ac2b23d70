#!/usr/bin/env python3
"""
Script de debug pour examiner toutes les structures G-Stock
"""

from api.models import GStockSortie, GStockConsommation, GStockAchat
import json

print("DEBUG TOUTES LES DONNÉES G-STOCK")
print("=" * 50)

# GStockSortie
print("\n1. G-STOCK SORTIE")
print("-" * 30)
sortie = GStockSortie.objects.order_by('-created_at').first()
if sortie:
    print(f"Titre: {sortie.title}")
    print(f"Type: {type(sortie.data)}")
    if isinstance(sortie.data, dict):
        print(f"Clés: {list(sortie.data.keys())}")
        print(f"Structure: {json.dumps(sortie.data, indent=2, default=str)[:500]}...")
else:
    print("Aucune donnée trouvée")

# GStockConsommation
print("\n2. G-STOCK CONSOMMATION")
print("-" * 30)
conso = GStockConsommation.objects.order_by('-created_at').first()
if conso:
    print(f"Titre: {conso.title}")
    print(f"Type: {type(conso.data)}")
    if isinstance(conso.data, dict):
        print(f"Clés: {list(conso.data.keys())}")
        print(f"Structure: {json.dumps(conso.data, indent=2, default=str)[:500]}...")
else:
    print("Aucune donnée trouvée")

# GStockAchat
print("\n3. G-STOCK ACHAT")
print("-" * 30)
achat = GStockAchat.objects.order_by('-created_at').first()
if achat:
    print(f"Titre: {achat.title}")
    print(f"Type: {type(achat.data)}")
    if isinstance(achat.data, dict):
        print(f"Clés: {list(achat.data.keys())}")
        print(f"Structure: {json.dumps(achat.data, indent=2, default=str)[:500]}...")
else:
    print("Aucune donnée trouvée")
