#!/usr/bin/env python3
"""
Script de debug pour examiner la structure des données E-Syndic
"""

from api.models import ESyndic
import json

print("DEBUG E-SYNDIC")
print("=" * 50)

# Récupérer la dernière entrée
latest = ESyndic.objects.order_by('-created_at').first()

if latest:
    print(f"Titre: {latest.title}")
    print(f"Description: {latest.description}")
    print(f"Date: {latest.created_at}")
    print(f"Type de données: {type(latest.data)}")
    
    if isinstance(latest.data, dict):
        print(f"Clés principales: {list(latest.data.keys())}")
        
        if 'data' in latest.data:
            print(f"Type de data['data']: {type(latest.data['data'])}")
            if isinstance(latest.data['data'], list):
                print(f"Nombre d'éléments: {len(latest.data['data'])}")
                if len(latest.data['data']) > 0:
                    print(f"Premier élément: {list(latest.data['data'][0].keys())}")
        
        # Afficher la structure JSON de manière lisible
        print("\nStructure des données:")
        print(json.dumps(latest.data, indent=2, default=str)[:1000] + "...")
    else:
        print(f"Données: {latest.data}")
else:
    print("Aucune donnée trouvée")
