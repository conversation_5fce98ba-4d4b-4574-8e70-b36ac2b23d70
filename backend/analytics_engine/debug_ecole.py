#!/usr/bin/env python3
"""
Script de debug pour examiner la structure des données École des Talents
"""

from api.models import EcoleTalents
import json

print("DEBUG ÉCOLE DES TALENTS")
print("=" * 50)

# Récupérer la dernière entrée
latest = EcoleTalents.objects.order_by('-created_at').first()

if latest:
    print(f"Titre: {latest.title}")
    print(f"Description: {latest.description}")
    print(f"Date: {latest.created_at}")
    print(f"Type de données: {type(latest.data)}")
    
    if isinstance(latest.data, dict):
        print(f"Clés principales: {list(latest.data.keys())}")
        
        if 'data' in latest.data:
            print(f"Type de data['data']: {type(latest.data['data'])}")
            if isinstance(latest.data['data'], dict):
                print(f"Clés de data['data']: {list(latest.data['data'].keys())}")
                if 'data' in latest.data['data']:
                    print(f"Type de data['data']['data']: {type(latest.data['data']['data'])}")
                    if isinstance(latest.data['data']['data'], list):
                        print(f"Nombre d'éléments dans la liste: {len(latest.data['data']['data'])}")
                        if len(latest.data['data']['data']) > 0:
                            print(f"Premier élément: {latest.data['data']['data'][0]}")
        
        # Afficher la structure JSON de manière lisible
        print("\nStructure des données:")
        print(json.dumps(latest.data, indent=2, default=str)[:1000] + "...")
    else:
        print(f"Données: {latest.data}")
else:
    print("Aucune donnée trouvée")
