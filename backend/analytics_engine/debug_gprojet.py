#!/usr/bin/env python3
"""
Script de debug pour examiner la structure des données G-Projet
"""

from api.models import GProjet
import json

print("DEBUG G-PROJET")
print("=" * 50)

# Récupérer la dernière entrée
latest = GProjet.objects.order_by('-created_at').first()

if latest:
    print(f"Titre: {latest.title}")
    print(f"Description: {latest.description}")
    print(f"Date: {latest.created_at}")
    print(f"Type de données: {type(latest.data)}")
    
    if isinstance(latest.data, dict):
        print(f"Clés principales: {list(latest.data.keys())}")
        
        # Afficher la structure JSON de manière lisible
        print("\nStructure des données:")
        print(json.dumps(latest.data, indent=2, default=str)[:1000] + "...")
    else:
        print(f"Données: {latest.data}")
else:
    print("Aucune donnée trouvée")
