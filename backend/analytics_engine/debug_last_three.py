#!/usr/bin/env python3
"""
Script de debug pour examiner les 3 dernières structures de données
"""

from api.models import Sentinelle, CRM, UserProfile
import json

print("DEBUG DES 3 DERNIÈRES CLASSES")
print("=" * 50)

# 1. <PERSON>le
print("\n1. SENTINELLE")
print("-" * 30)
try:
    sentinelle = Sentinelle.objects.first()
    if sentinelle:
        print(f"Titre: {sentinelle.title}")
        print(f"Type: {type(sentinelle.data)}")
        if isinstance(sentinelle.data, dict):
            print(f"Clés: {list(sentinelle.data.keys())}")
            print(f"Structure: {json.dumps(sentinelle.data, indent=2, default=str)[:500]}...")
    else:
        print("Aucune donnée trouvée")
except Exception as e:
    print(f"Erreur: {e}")

# 2. CRM
print("\n2. CRM")
print("-" * 30)
try:
    crm = CRM.objects.first()
    if crm:
        print(f"Titre: {crm.title}")
        print(f"Type: {type(crm.data)}")
        if isinstance(crm.data, dict):
            print(f"Clés: {list(crm.data.keys())}")
            print(f"Structure: {json.dumps(crm.data, indent=2, default=str)[:500]}...")
    else:
        print("Aucune donnée trouvée")
except Exception as e:
    print(f"Erreur: {e}")

# 3. UserProfile
print("\n3. USER PROFILE")
print("-" * 30)
try:
    userprofile = UserProfile.objects.first()
    if userprofile:
        print(f"ID: {userprofile.id}")
        print(f"User ID: {userprofile.user_id}")
        print(f"Champs disponibles: {[f.name for f in UserProfile._meta.fields]}")
    else:
        print("Aucune donnée trouvée")
except Exception as e:
    print(f"Erreur: {e}")
