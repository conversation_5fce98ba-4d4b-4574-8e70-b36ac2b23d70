#!/usr/bin/env python3
"""
Script de debug pour examiner la structure des données G-Stock
"""

from api.models import GStockApprovisionnement
import json

print("DEBUG G-STOCK APPROVISIONNEMENT")
print("=" * 50)

# Récupérer la dernière entrée
latest = GStockApprovisionnement.objects.order_by('-created_at').first()

if latest:
    print(f"Titre: {latest.title}")
    print(f"Description: {latest.description}")
    print(f"Date: {latest.created_at}")
    print(f"Type de données: {type(latest.data)}")
    
    if isinstance(latest.data, dict):
        print(f"Clés principales: {list(latest.data.keys())}")
        
        # Afficher la structure JSON de manière lisible
        print("\nStructure des données:")
        print(json.dumps(latest.data, indent=2, default=str)[:1000] + "...")
    else:
        print(f"Données: {latest.data}")
else:
    print("Aucune donnée trouvée")
