# analytics_engine.py
import json
import pandas as pd
from decimal import Decimal
from datetime import datetime, timedelta
from django.db.models import Q, Avg, Sum, Count
from django.utils import timezone
from api.models import (
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
    GLocative,
)
from .models import (
    ProjectAnalytics,
    StockAnalytics,
    BusinessKPIs,
    AlertSystem,
)


class DQEAnalytics:
    """
    Classe d'analyse pour les données DQE (Devis Quantitatif Estimatif)
    Gère le chargement, nettoyage et affichage des données DQE
    """

    def __init__(self):
        self.model = DQEData
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_materiaux = None
        self.df_main_oeuvre = None
        self.df_prix_unitaires = None
        self.df_totaux_categories = None

    def load_data(self):
        """
        Charge les données DQE depuis la base de données
        """
        try:
            # Récupérer la dernière entrée DQE
            latest_dqe = self.model.objects.order_by('-created_at').first()

            if not latest_dqe:
                print("Aucune donnée DQE trouvée dans la base de données")
                return False

            self.data = latest_dqe.data
            print(f"Données DQE chargées avec succès: {latest_dqe.title}")
            print(f"Date de création: {latest_dqe.created_at}")

            # Convertir en DataFrame pour traitement initial
            if isinstance(self.data, dict) and 'data' in self.data:
                self._parse_dqe_structure()
                return True
            else:
                print("Structure de données DQE invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement des données DQE: {str(e)}")
            return False

    def _parse_dqe_structure(self):
        """
        Parse la structure complexe des données DQE
        """
        try:
            dqe_data = self.data['data']
            all_items = []

            # Parcourir la structure hiérarchique
            for program_data in dqe_data:
                program_name = program_data.get('program', {}).get('name', 'Programme Inconnu')

                for actif in program_data.get('actifs', []):
                    actif_info = actif.get('actif', {})
                    type_actif = actif_info.get('type_actif', {}).get('name', 'Type Inconnu')

                    for corps_etat in actif.get('corps_etats', []):
                        corps_etat_name = corps_etat.get('corps_etat', {}).get('name', 'Corps État Inconnu')

                        # Extraire récursivement tous les items
                        self._extract_items_recursive(
                            corps_etat.get('dataDqe', []),
                            all_items,
                            program_name,
                            type_actif,
                            corps_etat_name
                        )

            # Créer le DataFrame brut
            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure DQE parsée: {len(all_items)} éléments extraits")

        except Exception as e:
            print(f"Erreur lors du parsing de la structure DQE: {str(e)}")

    def _extract_items_recursive(self, items, all_items, program_name, type_actif, corps_etat_name, parent_lot=""):
        """
        Extrait récursivement tous les items de la structure DQE
        """
        for item in items:
            if 'items' in item and item['items']:
                # Item avec sous-items - continuer récursivement
                current_lot = f"{parent_lot}.{item.get('lot', '')}" if parent_lot else item.get('lot', '')
                self._extract_items_recursive(
                    item['items'],
                    all_items,
                    program_name,
                    type_actif,
                    corps_etat_name,
                    current_lot
                )
            else:
                # Item final - extraire les données
                item_data = {
                    'Programme': program_name,
                    'Type_Actif': type_actif,
                    'Corps_Etat': corps_etat_name,
                    'Lot': f"{parent_lot}.{item.get('lot', '')}" if parent_lot else item.get('lot', ''),
                    'ID': item.get('id'),
                    'Nom': item.get('nom', item.get('name', '')),
                    'Code': item.get('code', ''),
                    'Type': item.get('type', {}).get('name', ''),
                    'Quantite': item.get('quantite', 0),
                    'Base_Qty': item.get('base_qty', 0),
                    'PU_Total': item.get('pu_total', 0),
                    'PT_Total': item.get('pt_total', 0),
                    'PU_Materiel': item.get('pu_materiel', 0),
                    'PT_Materiel': item.get('pt_materiel', 0),
                    'PU_Main_Oeuvre': item.get('pu_main_oeuvre', 0),
                    'PT_Main_Oeuvre': item.get('pt_main_oeuvre', 0),
                    'PU_Coef_V': item.get('pu_coef_v', 0),
                    'PT_Coef_V': item.get('pt_coef_v', 0),
                    'Status': item.get('status', 0),
                    'Created_At': item.get('created_at', ''),
                    'Updated_At': item.get('updated_at', '')
                }

                # Ajouter les informations de prix si disponibles
                if 'prices' in item and item['prices']:
                    price_info = item['prices'][0]  # Prendre le premier prix
                    item_data['Gamme'] = price_info.get('gamme', {}).get('name', '')
                    item_data['Prix_Gamme'] = price_info.get('price', 0)

                # Ajouter les informations d'unité si disponibles
                if 'unites' in item and item['unites']:
                    unite_info = item['unites'][0]  # Prendre la première unité
                    item_data['Unite_Code'] = unite_info.get('code', '')
                    item_data['Unite_Name'] = unite_info.get('name', '')

                all_items.append(item_data)

    def clean_data(self):
        """
        Nettoie et formate les données DQE
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            # Copier les données brutes
            self.df_clean = self.df_raw.copy()

            # Supprimer les colonnes inutiles pour l'analyse
            columns_to_remove = ['ID', 'Created_At', 'Updated_At', 'Status']
            self.df_clean = self.df_clean.drop(columns=[col for col in columns_to_remove if col in self.df_clean.columns])

            # Nettoyer les valeurs numériques
            numeric_columns = ['Quantite', 'Base_Qty', 'PU_Total', 'PT_Total', 'PU_Materiel',
                             'PT_Materiel', 'PU_Main_Oeuvre', 'PT_Main_Oeuvre', 'PU_Coef_V',
                             'PT_Coef_V', 'Prix_Gamme']

            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Programme', 'Type_Actif', 'Corps_Etat', 'Nom', 'Code', 'Type', 'Gamme', 'Unite_Code', 'Unite_Name']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip()
                    # Première lettre en majuscule pour les noms
                    if col in ['Nom', 'Type', 'Gamme']:
                        self.df_clean[col] = self.df_clean[col].str.title()

            # Filtrer les lignes avec des données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Nom'] != '') &
                (self.df_clean['Nom'] != 'nan') &
                (self.df_clean['PT_Total'] > 0)
            ]

            # Créer les DataFrames spécialisés
            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage des données: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des matériaux
        self.df_materiaux = self.df_clean[self.df_clean['Type'] == 'Materiel'].copy()
        if not self.df_materiaux.empty:
            self.df_materiaux = self.df_materiaux[['Programme', 'Corps_Etat', 'Nom', 'Code', 'Quantite',
                                                 'Unite_Name', 'PU_Materiel', 'PT_Materiel', 'Gamme']].copy()

        # DataFrame de la main d'œuvre
        self.df_main_oeuvre = self.df_clean[self.df_clean['PT_Main_Oeuvre'] > 0].copy()
        if not self.df_main_oeuvre.empty:
            self.df_main_oeuvre = self.df_main_oeuvre[['Programme', 'Corps_Etat', 'Nom', 'Quantite',
                                                     'PU_Main_Oeuvre', 'PT_Main_Oeuvre']].copy()

        # DataFrame des prix unitaires
        self.df_prix_unitaires = self.df_clean[['Programme', 'Corps_Etat', 'Nom', 'Type',
                                              'PU_Total', 'Unite_Name', 'Gamme']].copy()

        # DataFrame des totaux par catégorie
        self.df_totaux_categories = self.df_clean.groupby(['Programme', 'Corps_Etat', 'Type']).agg({
            'PT_Total': 'sum',
            'PT_Materiel': 'sum',
            'PT_Main_Oeuvre': 'sum',
            'PT_Coef_V': 'sum',
            'Quantite': 'count'
        }).reset_index()

        self.df_totaux_categories.columns = ['Programme', 'Corps_Etat', 'Type', 'Total_Prix',
                                           'Total_Materiel', 'Total_Main_Oeuvre', 'Total_Coef_V', 'Nombre_Items']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES DQE")
        print("="*80)

        # Résumé général
        print(f"\nRésumé général:")
        print(f"- Nombre total d'éléments: {len(self.df_clean):,}")
        print(f"- Programmes: {self.df_clean['Programme'].nunique()}")
        print(f"- Corps d'état: {self.df_clean['Corps_Etat'].nunique()}")
        print(f"- Types d'éléments: {self.df_clean['Type'].nunique()}")
        print(f"- Montant total: {self.df_clean['PT_Total'].sum():,.0f} FCFA")

        # Affichage des matériaux
        if not self.df_materiaux.empty:
            print(f"\n📦 MATÉRIAUX ({len(self.df_materiaux)} éléments)")
            print("-" * 50)
            print(self.df_materiaux.head(10).to_string())

            print(f"\nTop 5 matériaux par coût:")
            top_materiaux = self.df_materiaux.nlargest(5, 'PT_Materiel')[['Nom', 'PT_Materiel', 'Quantite', 'Unite_Name']]
            print(top_materiaux.to_string())

        # Affichage de la main d'œuvre
        if not self.df_main_oeuvre.empty:
            print(f"\n👷 MAIN D'ŒUVRE ({len(self.df_main_oeuvre)} éléments)")
            print("-" * 50)
            print(self.df_main_oeuvre.head(10).to_string())

            print(f"\nTop 5 postes main d'œuvre par coût:")
            top_main_oeuvre = self.df_main_oeuvre.nlargest(5, 'PT_Main_Oeuvre')[['Nom', 'PT_Main_Oeuvre', 'Quantite']]
            print(top_main_oeuvre.to_string())

        # Affichage des prix unitaires
        print(f"\n💰 PRIX UNITAIRES ({len(self.df_prix_unitaires)} éléments)")
        print("-" * 50)
        print(self.df_prix_unitaires.head(10).to_string())

        # Affichage des totaux par catégorie
        print(f"\n📊 TOTAUX PAR CATÉGORIE")
        print("-" * 50)
        print(self.df_totaux_categories.to_string())

        # Répartition par programme
        print(f"\n🏗️ RÉPARTITION PAR PROGRAMME")
        print("-" * 50)
        repartition_programme = self.df_clean.groupby('Programme')['PT_Total'].sum().sort_values(ascending=False)
        for programme, total in repartition_programme.items():
            pourcentage = (total / self.df_clean['PT_Total'].sum()) * 100
            print(f"  {programme}: {total:,.0f} FCFA ({pourcentage:.1f}%)")

        # Répartition par corps d'état
        print(f"\n🔧 RÉPARTITION PAR CORPS D'ÉTAT")
        print("-" * 50)
        repartition_corps = self.df_clean.groupby('Corps_Etat')['PT_Total'].sum().sort_values(ascending=False)
        for corps, total in repartition_corps.items():
            pourcentage = (total / self.df_clean['PT_Total'].sum()) * 100
            print(f"  {corps}: {total:,.0f} FCFA ({pourcentage:.1f}%)")


class GStockApprovisionnementAnalytics:
    """
    Classe d'analyse pour les données G-Stock Approvisionnement
    Gère le chargement, nettoyage et affichage des données d'approvisionnement
    """

    def __init__(self):
        self.model = GStockApprovisionnement
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_approvisionnements = None
        self.df_produits = None
        self.df_fournisseurs = None
        self.df_programmes = None

    def load_data(self):
        """
        Charge les données G-Stock Approvisionnement depuis la base de données
        """
        try:
            latest_appro = self.model.objects.order_by('-created_at').first()

            if not latest_appro:
                print("Aucune donnée G-Stock Approvisionnement trouvée")
                return False

            self.data = latest_appro.data
            print(f"Données G-Stock Approvisionnement chargées: {latest_appro.title}")
            print(f"Date de création: {latest_appro.created_at}")

            if isinstance(self.data, dict) and 'stocks' in self.data:
                self._parse_approvisionnement_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_approvisionnement_structure(self):
        """
        Parse la structure des données d'approvisionnement
        """
        try:
            all_items = []

            for stock in self.data.get('stocks', []):
                programme_info = stock.get('programme', {})
                programme_nom = programme_info.get('nom', 'Programme Inconnu')
                programme_id = programme_info.get('id', 0)

                for appro in stock.get('approvisionnements', []):
                    appro_id = appro.get('id', 0)
                    ref = appro.get('ref', '')
                    created_at = appro.get('created_at', '')
                    fournisseur_id = appro.get('fournisseur_id', 0)

                    for item in appro.get('items', []):
                        produit_info = item.get('produits', {})

                        item_data = {
                            'Programme_ID': programme_id,
                            'Programme_Nom': programme_nom,
                            'Approvisionnement_ID': appro_id,
                            'Reference': ref,
                            'Date_Creation': created_at,
                            'Fournisseur_ID': fournisseur_id,
                            'Item_ID': item.get('id', 0),
                            'Produit_ID': item.get('produit_id', 0),
                            'Produit_Code': produit_info.get('code', ''),
                            'Produit_Libelle': produit_info.get('libelle', ''),
                            'Quantite': item.get('qte', 0)
                        }

                        all_items.append(item_data)

            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure parsée: {len(all_items)} éléments extraits")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données d'approvisionnement
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            numeric_columns = ['Programme_ID', 'Approvisionnement_ID', 'Fournisseur_ID',
                             'Item_ID', 'Produit_ID', 'Quantite']

            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Programme_Nom', 'Reference', 'Produit_Code', 'Produit_Libelle']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip()
                    if col in ['Programme_Nom', 'Produit_Libelle']:
                        self.df_clean[col] = self.df_clean[col].str.title()

            # Convertir les dates
            if 'Date_Creation' in self.df_clean.columns:
                self.df_clean['Date_Creation'] = pd.to_datetime(self.df_clean['Date_Creation'], errors='coerce')

            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Quantite'] > 0) &
                (self.df_clean['Produit_Libelle'] != '') &
                (self.df_clean['Produit_Libelle'] != 'nan')
            ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des approvisionnements
        self.df_approvisionnements = self.df_clean.groupby(['Approvisionnement_ID', 'Reference', 'Programme_Nom', 'Date_Creation']).agg({
            'Quantite': 'sum',
            'Produit_ID': 'count'
        }).reset_index()
        self.df_approvisionnements.columns = ['Approvisionnement_ID', 'Reference', 'Programme', 'Date_Creation', 'Quantite_Totale', 'Nombre_Produits']

        # DataFrame des produits
        self.df_produits = self.df_clean.groupby(['Produit_Code', 'Produit_Libelle']).agg({
            'Quantite': 'sum',
            'Approvisionnement_ID': 'nunique'
        }).reset_index()
        self.df_produits.columns = ['Code_Produit', 'Libelle_Produit', 'Quantite_Totale', 'Nombre_Approvisionnements']

        # DataFrame des fournisseurs
        self.df_fournisseurs = self.df_clean.groupby('Fournisseur_ID').agg({
            'Quantite': 'sum',
            'Approvisionnement_ID': 'nunique',
            'Produit_ID': 'nunique'
        }).reset_index()
        self.df_fournisseurs.columns = ['Fournisseur_ID', 'Quantite_Totale', 'Nombre_Approvisionnements', 'Nombre_Produits']

        # DataFrame des programmes
        self.df_programmes = self.df_clean.groupby(['Programme_ID', 'Programme_Nom']).agg({
            'Quantite': 'sum',
            'Approvisionnement_ID': 'nunique',
            'Fournisseur_ID': 'nunique'
        }).reset_index()
        self.df_programmes.columns = ['Programme_ID', 'Programme_Nom', 'Quantite_Totale', 'Nombre_Approvisionnements', 'Nombre_Fournisseurs']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES G-STOCK APPROVISIONNEMENT")
        print("="*80)

        # Résumé général
        print(f"\nRésumé général:")
        print(f"- Nombre total d'éléments: {len(self.df_clean):,}")
        print(f"- Programmes: {self.df_clean['Programme_Nom'].nunique()}")
        print(f"- Approvisionnements: {self.df_clean['Approvisionnement_ID'].nunique()}")
        print(f"- Fournisseurs: {self.df_clean['Fournisseur_ID'].nunique()}")
        print(f"- Produits différents: {self.df_clean['Produit_ID'].nunique()}")
        print(f"- Quantité totale: {self.df_clean['Quantite'].sum():,.0f}")

        # Affichage des approvisionnements
        if not self.df_approvisionnements.empty:
            print(f"\nAPPROVISIONNEMENTS ({len(self.df_approvisionnements)} éléments)")
            print("-" * 50)
            print(self.df_approvisionnements.head(10).to_string())

            print(f"\nTop 5 approvisionnements par quantité:")
            top_appro = self.df_approvisionnements.nlargest(5, 'Quantite_Totale')[['Reference', 'Programme', 'Quantite_Totale', 'Nombre_Produits']]
            print(top_appro.to_string())

        # Affichage des produits
        if not self.df_produits.empty:
            print(f"\nPRODUITS ({len(self.df_produits)} éléments)")
            print("-" * 50)
            print(self.df_produits.head(10).to_string())

            print(f"\nTop 5 produits par quantité:")
            top_produits = self.df_produits.nlargest(5, 'Quantite_Totale')[['Code_Produit', 'Libelle_Produit', 'Quantite_Totale']]
            print(top_produits.to_string())

        # Affichage des fournisseurs
        if not self.df_fournisseurs.empty:
            print(f"\nFOURNISSEURS ({len(self.df_fournisseurs)} éléments)")
            print("-" * 50)
            print(self.df_fournisseurs.head(10).to_string())

            print(f"\nTop 5 fournisseurs par quantité:")
            top_fournisseurs = self.df_fournisseurs.nlargest(5, 'Quantite_Totale')[['Fournisseur_ID', 'Quantite_Totale', 'Nombre_Approvisionnements']]
            print(top_fournisseurs.to_string())

        # Affichage des programmes
        if not self.df_programmes.empty:
            print(f"\nPROGRAMMES ({len(self.df_programmes)} éléments)")
            print("-" * 50)
            print(self.df_programmes.to_string())

        # Répartition par programme
        print(f"\nRÉPARTITION PAR PROGRAMME")
        print("-" * 50)
        repartition_programme = self.df_clean.groupby('Programme_Nom')['Quantite'].sum().sort_values(ascending=False)
        for programme, total in repartition_programme.items():
            pourcentage = (total / self.df_clean['Quantite'].sum()) * 100
            print(f"  {programme}: {total:,.0f} unités ({pourcentage:.1f}%)")


class EcoleTalentsAnalytics:
    """
    Classe d'analyse pour les données École des Talents
    Gère le chargement, nettoyage et affichage des données de formation
    """

    def __init__(self):
        self.model = EcoleTalents
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_promotions = None
        self.df_inscriptions = None
        self.df_metiers = None

    def load_data(self):
        """
        Charge les données École des Talents depuis la base de données
        """
        try:
            latest_ecole = self.model.objects.order_by('-created_at').first()

            if not latest_ecole:
                print("Aucune donnée École des Talents trouvée")
                return False

            self.data = latest_ecole.data
            print(f"Données École des Talents chargées: {latest_ecole.title}")
            print(f"Date de création: {latest_ecole.created_at}")

            if isinstance(self.data, dict) and 'data' in self.data:
                self._parse_ecole_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_ecole_structure(self):
        """
        Parse la structure des données École des Talents
        """
        try:
            ecole_data = self.data['data']
            all_promotions = []
            all_inscriptions = []

            for promotion in ecole_data:
                # Données de base de la promotion
                promo_data = {
                    'Promotion_ID': promotion.get('id', 0),
                    'Promotion_Nom': promotion.get('name', ''),
                    'Total_General': promotion.get('total_general', 0),
                    'Total_Eligible': promotion.get('total_eligible', 0),
                    'Total_NonEligible': promotion.get('total_noneligible', 0)
                }
                all_promotions.append(promo_data)

                # Inscriptions par métier
                for inscription in promotion.get('inscription_par_metier', []):
                    inscription_data = {
                        'Promotion_ID': promotion.get('id', 0),
                        'Promotion_Nom': promotion.get('name', ''),
                        'Metier': inscription.get('metier', ''),
                        'Total_Inscriptions': inscription.get('total', 0),
                        'Total_Eligible': inscription.get('eligible', 0),
                        'Total_NonEligible': inscription.get('noneligible', 0)
                    }
                    all_inscriptions.append(inscription_data)

            self.df_raw = pd.DataFrame(all_promotions)
            self.df_inscriptions_raw = pd.DataFrame(all_inscriptions)
            print(f"Structure parsée: {len(all_promotions)} promotions, {len(all_inscriptions)} inscriptions par métier")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données École des Talents
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            # Nettoyer les données des promotions
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            numeric_columns = ['Promotion_ID', 'Total_General', 'Total_Eligible', 'Total_NonEligible']
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            if 'Promotion_Nom' in self.df_clean.columns:
                self.df_clean['Promotion_Nom'] = self.df_clean['Promotion_Nom'].astype(str).str.strip().str.title()

            # Nettoyer les données des inscriptions
            if hasattr(self, 'df_inscriptions_raw') and not self.df_inscriptions_raw.empty:
                self.df_inscriptions = self.df_inscriptions_raw.copy()

                # Nettoyer les valeurs numériques des inscriptions
                numeric_cols_inscr = ['Promotion_ID', 'Total_Inscriptions', 'Total_Eligible', 'Total_NonEligible']
                for col in numeric_cols_inscr:
                    if col in self.df_inscriptions.columns:
                        self.df_inscriptions[col] = pd.to_numeric(self.df_inscriptions[col], errors='coerce').fillna(0)

                # Nettoyer les chaînes de caractères des inscriptions
                string_cols_inscr = ['Promotion_Nom', 'Metier']
                for col in string_cols_inscr:
                    if col in self.df_inscriptions.columns:
                        self.df_inscriptions[col] = self.df_inscriptions[col].astype(str).str.strip().str.title()

                # Filtrer les données valides
                self.df_inscriptions = self.df_inscriptions[
                    (self.df_inscriptions['Total_Inscriptions'] > 0) &
                    (self.df_inscriptions['Metier'] != '') &
                    (self.df_inscriptions['Metier'] != 'nan')
                ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} promotions, {len(self.df_inscriptions) if hasattr(self, 'df_inscriptions') else 0} inscriptions")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des promotions (déjà dans df_clean)
        self.df_promotions = self.df_clean.copy()

        # DataFrame des métiers (agrégation des inscriptions par métier)
        if hasattr(self, 'df_inscriptions') and not self.df_inscriptions.empty:
            self.df_metiers = self.df_inscriptions.groupby('Metier').agg({
                'Total_Inscriptions': 'sum',
                'Total_Eligible': 'sum',
                'Total_NonEligible': 'sum',
                'Promotion_ID': 'nunique'
            }).reset_index()
            self.df_metiers.columns = ['Metier', 'Total_Inscriptions', 'Total_Eligible', 'Total_NonEligible', 'Nombre_Promotions']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES ÉCOLE DES TALENTS")
        print("="*80)

        # Résumé général
        total_inscriptions = self.df_clean['Total_General'].sum()
        total_eligible = self.df_clean['Total_Eligible'].sum()
        taux_eligibilite = (total_eligible / total_inscriptions * 100) if total_inscriptions > 0 else 0

        print(f"\nRésumé général:")
        print(f"- Nombre de promotions: {len(self.df_clean)}")
        print(f"- Total inscriptions: {total_inscriptions:,}")
        print(f"- Total éligibles: {total_eligible:,}")
        print(f"- Taux d'éligibilité global: {taux_eligibilite:.1f}%")

        # Affichage des promotions
        print(f"\nPROMOTIONS ({len(self.df_promotions)} éléments)")
        print("-" * 50)
        print(self.df_promotions.to_string())

        # Affichage des inscriptions par métier
        if hasattr(self, 'df_inscriptions') and not self.df_inscriptions.empty:
            print(f"\nINSCRIPTIONS PAR MÉTIER ({len(self.df_inscriptions)} éléments)")
            print("-" * 50)
            print(self.df_inscriptions.head(10).to_string())

            print(f"\nTop 5 métiers par inscriptions:")
            top_metiers = self.df_inscriptions.nlargest(5, 'Total_Inscriptions')[['Metier', 'Total_Inscriptions', 'Total_Eligible', 'Promotion_Nom']]
            print(top_metiers.to_string())

        # Affichage des métiers agrégés
        if hasattr(self, 'df_metiers') and not self.df_metiers.empty:
            print(f"\nMÉTIERS AGRÉGÉS ({len(self.df_metiers)} éléments)")
            print("-" * 50)
            print(self.df_metiers.to_string())

        # Répartition par promotion
        print(f"\nRÉPARTITION PAR PROMOTION")
        print("-" * 50)
        for _, promo in self.df_promotions.iterrows():
            pourcentage = (promo['Total_General'] / total_inscriptions * 100) if total_inscriptions > 0 else 0
            taux_promo = (promo['Total_Eligible'] / promo['Total_General'] * 100) if promo['Total_General'] > 0 else 0
            print(f"  {promo['Promotion_Nom']}: {promo['Total_General']:,} inscriptions ({pourcentage:.1f}%) - {taux_promo:.1f}% éligibles")


class GStockSortieAnalytics:
    """
    Classe d'analyse pour les données G-Stock Sortie
    Gère le chargement, nettoyage et affichage des données de sortie de stock
    """

    def __init__(self):
        self.model = GStockSortie
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_sorties = None
        self.df_destinations = None
        self.df_materiaux = None

    def load_data(self):
        """
        Charge les données G-Stock Sortie depuis la base de données
        """
        try:
            latest_sortie = self.model.objects.order_by('-created_at').first()

            if not latest_sortie:
                print("Aucune donnée G-Stock Sortie trouvée")
                return False

            self.data = latest_sortie.data
            print(f"Données G-Stock Sortie chargées: {latest_sortie.title}")
            print(f"Date de création: {latest_sortie.created_at}")

            if isinstance(self.data, dict):
                self._parse_sortie_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_sortie_structure(self):
        """
        Parse la structure des données de sortie
        """
        try:
            all_items = []

            # Structure simple basée sur les exemples JSON
            item_data = {
                'Materiau': self.data.get('item_name', ''),
                'Quantite_Sortie': self.data.get('quantity_out', 0),
                'Unite': self.data.get('unit', ''),
                'Destination': self.data.get('destination', ''),
                'Date_Sortie': self.data.get('exit_date', '')
            }
            all_items.append(item_data)

            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure parsée: {len(all_items)} éléments extraits")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données de sortie
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            if 'Quantite_Sortie' in self.df_clean.columns:
                self.df_clean['Quantite_Sortie'] = pd.to_numeric(self.df_clean['Quantite_Sortie'], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Materiau', 'Unite', 'Destination']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()

            # Convertir les dates
            if 'Date_Sortie' in self.df_clean.columns:
                self.df_clean['Date_Sortie'] = pd.to_datetime(self.df_clean['Date_Sortie'], errors='coerce')

            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Quantite_Sortie'] > 0) &
                (self.df_clean['Materiau'] != '') &
                (self.df_clean['Materiau'] != 'nan')
            ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des sorties (déjà dans df_clean)
        self.df_sorties = self.df_clean.copy()

        # DataFrame des destinations
        if not self.df_clean.empty:
            self.df_destinations = self.df_clean.groupby('Destination').agg({
                'Quantite_Sortie': 'sum',
                'Materiau': 'count'
            }).reset_index()
            self.df_destinations.columns = ['Destination', 'Quantite_Totale', 'Nombre_Sorties']

            # DataFrame des matériaux
            self.df_materiaux = self.df_clean.groupby(['Materiau', 'Unite']).agg({
                'Quantite_Sortie': 'sum',
                'Destination': 'nunique'
            }).reset_index()
            self.df_materiaux.columns = ['Materiau', 'Unite', 'Quantite_Totale', 'Nombre_Destinations']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES G-STOCK SORTIE")
        print("="*80)

        # Résumé général
        print(f"\nRésumé général:")
        print(f"- Nombre de sorties: {len(self.df_clean)}")
        print(f"- Quantité totale sortie: {self.df_clean['Quantite_Sortie'].sum():,.0f}")
        print(f"- Destinations différentes: {self.df_clean['Destination'].nunique()}")
        print(f"- Matériaux différents: {self.df_clean['Materiau'].nunique()}")

        # Affichage des sorties
        print(f"\nSORTIES DE STOCK ({len(self.df_sorties)} éléments)")
        print("-" * 50)
        print(self.df_sorties.to_string())

        # Affichage des destinations
        if hasattr(self, 'df_destinations') and not self.df_destinations.empty:
            print(f"\nDESTINATIONS ({len(self.df_destinations)} éléments)")
            print("-" * 50)
            print(self.df_destinations.to_string())

        # Affichage des matériaux
        if hasattr(self, 'df_materiaux') and not self.df_materiaux.empty:
            print(f"\nMATÉRIAUX ({len(self.df_materiaux)} éléments)")
            print("-" * 50)
            print(self.df_materiaux.to_string())


class GStockConsommationAnalytics:
    """
    Classe d'analyse pour les données G-Stock Consommation
    Gère le chargement, nettoyage et affichage des données de consommation
    """

    def __init__(self):
        self.model = GStockConsommation
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_consommations = None
        self.df_projets = None
        self.df_materiaux = None

    def load_data(self):
        """
        Charge les données G-Stock Consommation depuis la base de données
        """
        try:
            latest_conso = self.model.objects.order_by('-created_at').first()

            if not latest_conso:
                print("Aucune donnée G-Stock Consommation trouvée")
                return False

            self.data = latest_conso.data
            print(f"Données G-Stock Consommation chargées: {latest_conso.title}")
            print(f"Date de création: {latest_conso.created_at}")

            if isinstance(self.data, dict):
                self._parse_consommation_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_consommation_structure(self):
        """
        Parse la structure des données de consommation
        """
        try:
            all_items = []

            # Structure simple basée sur les exemples JSON
            item_data = {
                'Materiau': self.data.get('item_name', ''),
                'Quantite_Consommee': self.data.get('quantity_consumed', 0),
                'Unite': self.data.get('unit', ''),
                'Projet': self.data.get('project', ''),
                'Date_Consommation': self.data.get('consumption_date', '')
            }
            all_items.append(item_data)

            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure parsée: {len(all_items)} éléments extraits")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données de consommation
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            if 'Quantite_Consommee' in self.df_clean.columns:
                self.df_clean['Quantite_Consommee'] = pd.to_numeric(self.df_clean['Quantite_Consommee'], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Materiau', 'Unite', 'Projet']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()

            # Convertir les dates
            if 'Date_Consommation' in self.df_clean.columns:
                self.df_clean['Date_Consommation'] = pd.to_datetime(self.df_clean['Date_Consommation'], errors='coerce')

            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Quantite_Consommee'] > 0) &
                (self.df_clean['Materiau'] != '') &
                (self.df_clean['Materiau'] != 'nan')
            ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des consommations (déjà dans df_clean)
        self.df_consommations = self.df_clean.copy()

        # DataFrame des projets
        if not self.df_clean.empty:
            self.df_projets = self.df_clean.groupby('Projet').agg({
                'Quantite_Consommee': 'sum',
                'Materiau': 'count'
            }).reset_index()
            self.df_projets.columns = ['Projet', 'Quantite_Totale', 'Nombre_Consommations']

            # DataFrame des matériaux
            self.df_materiaux = self.df_clean.groupby(['Materiau', 'Unite']).agg({
                'Quantite_Consommee': 'sum',
                'Projet': 'nunique'
            }).reset_index()
            self.df_materiaux.columns = ['Materiau', 'Unite', 'Quantite_Totale', 'Nombre_Projets']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES G-STOCK CONSOMMATION")
        print("="*80)

        # Résumé général
        print(f"\nRésumé général:")
        print(f"- Nombre de consommations: {len(self.df_clean)}")
        print(f"- Quantité totale consommée: {self.df_clean['Quantite_Consommee'].sum():,.0f}")
        print(f"- Projets différents: {self.df_clean['Projet'].nunique()}")
        print(f"- Matériaux différents: {self.df_clean['Materiau'].nunique()}")

        # Affichage des consommations
        print(f"\nCONSOMMATIONS ({len(self.df_consommations)} éléments)")
        print("-" * 50)
        print(self.df_consommations.to_string())

        # Affichage des projets
        if hasattr(self, 'df_projets') and not self.df_projets.empty:
            print(f"\nPROJETS ({len(self.df_projets)} éléments)")
            print("-" * 50)
            print(self.df_projets.to_string())

        # Affichage des matériaux
        if hasattr(self, 'df_materiaux') and not self.df_materiaux.empty:
            print(f"\nMATÉRIAUX ({len(self.df_materiaux)} éléments)")
            print("-" * 50)
            print(self.df_materiaux.to_string())


class GStockAchatAnalytics:
    """
    Classe d'analyse pour les données G-Stock Achat
    Gère le chargement, nettoyage et affichage des données d'achat
    """

    def __init__(self):
        self.model = GStockAchat
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_achats = None
        self.df_fournisseurs = None
        self.df_materiaux = None

    def load_data(self):
        """
        Charge les données G-Stock Achat depuis la base de données
        """
        try:
            latest_achat = self.model.objects.order_by('-created_at').first()

            if not latest_achat:
                print("Aucune donnée G-Stock Achat trouvée")
                return False

            self.data = latest_achat.data
            print(f"Données G-Stock Achat chargées: {latest_achat.title}")
            print(f"Date de création: {latest_achat.created_at}")

            if isinstance(self.data, dict):
                self._parse_achat_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_achat_structure(self):
        """
        Parse la structure des données d'achat
        """
        try:
            all_items = []

            # Structure simple basée sur les exemples JSON
            item_data = {
                'Materiau': self.data.get('item_name', ''),
                'Quantite': self.data.get('quantity', 0),
                'Prix_Unitaire': self.data.get('unit_price', 0),
                'Prix_Total': self.data.get('total_price', 0),
                'Fournisseur': self.data.get('supplier', ''),
                'Date_Achat': self.data.get('purchase_date', '')
            }
            all_items.append(item_data)

            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure parsée: {len(all_items)} éléments extraits")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données d'achat
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            numeric_columns = ['Quantite', 'Prix_Unitaire', 'Prix_Total']
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Materiau', 'Fournisseur']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()

            # Convertir les dates
            if 'Date_Achat' in self.df_clean.columns:
                self.df_clean['Date_Achat'] = pd.to_datetime(self.df_clean['Date_Achat'], errors='coerce')

            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Quantite'] > 0) &
                (self.df_clean['Prix_Total'] > 0) &
                (self.df_clean['Materiau'] != '') &
                (self.df_clean['Materiau'] != 'nan')
            ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des achats (déjà dans df_clean)
        self.df_achats = self.df_clean.copy()

        # DataFrame des fournisseurs
        if not self.df_clean.empty:
            self.df_fournisseurs = self.df_clean.groupby('Fournisseur').agg({
                'Prix_Total': 'sum',
                'Quantite': 'sum',
                'Materiau': 'count'
            }).reset_index()
            self.df_fournisseurs.columns = ['Fournisseur', 'Montant_Total', 'Quantite_Totale', 'Nombre_Achats']

            # DataFrame des matériaux
            self.df_materiaux = self.df_clean.groupby('Materiau').agg({
                'Prix_Total': 'sum',
                'Quantite': 'sum',
                'Prix_Unitaire': 'mean',
                'Fournisseur': 'nunique'
            }).reset_index()
            self.df_materiaux.columns = ['Materiau', 'Montant_Total', 'Quantite_Totale', 'Prix_Moyen', 'Nombre_Fournisseurs']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES G-STOCK ACHAT")
        print("="*80)

        # Résumé général
        print(f"\nRésumé général:")
        print(f"- Nombre d'achats: {len(self.df_clean)}")
        print(f"- Montant total: {self.df_clean['Prix_Total'].sum():,.0f} FCFA")
        print(f"- Quantité totale: {self.df_clean['Quantite'].sum():,.0f}")
        print(f"- Fournisseurs différents: {self.df_clean['Fournisseur'].nunique()}")
        print(f"- Matériaux différents: {self.df_clean['Materiau'].nunique()}")

        # Affichage des achats
        print(f"\nACHATS ({len(self.df_achats)} éléments)")
        print("-" * 50)
        print(self.df_achats.to_string())

        # Affichage des fournisseurs
        if hasattr(self, 'df_fournisseurs') and not self.df_fournisseurs.empty:
            print(f"\nFOURNISSEURS ({len(self.df_fournisseurs)} éléments)")
            print("-" * 50)
            print(self.df_fournisseurs.to_string())

        # Affichage des matériaux
        if hasattr(self, 'df_materiaux') and not self.df_materiaux.empty:
            print(f"\nMATÉRIAUX ({len(self.df_materiaux)} éléments)")
            print("-" * 50)
            print(self.df_materiaux.to_string())


class GProjetAnalytics:
    """
    Classe d'analyse pour les données G-Projet
    Gère le chargement, nettoyage et affichage des données de projets
    """

    def __init__(self):
        self.model = GProjet
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_projets = None
        self.df_statuts = None
        self.df_budgets = None

    def load_data(self):
        """
        Charge les données G-Projet depuis la base de données
        """
        try:
            latest_projet = self.model.objects.order_by('-created_at').first()

            if not latest_projet:
                print("Aucune donnée G-Projet trouvée")
                return False

            self.data = latest_projet.data
            print(f"Données G-Projet chargées: {latest_projet.title}")
            print(f"Date de création: {latest_projet.created_at}")

            if isinstance(self.data, dict):
                self._parse_projet_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_projet_structure(self):
        """
        Parse la structure des données de projet
        """
        try:
            all_items = []

            # Structure simple basée sur les exemples JSON
            item_data = {
                'Nom_Projet': self.data.get('project_name', ''),
                'Statut': self.data.get('status', ''),
                'Budget': self.data.get('budget', 0),
                'Avancement': self.data.get('progress', 0),
                'Date_Debut': self.data.get('start_date', ''),
                'Date_Fin': self.data.get('end_date', '')
            }
            all_items.append(item_data)

            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure parsée: {len(all_items)} éléments extraits")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données de projet
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            numeric_columns = ['Budget', 'Avancement']
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Nom_Projet', 'Statut']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()

            # Convertir les dates
            date_columns = ['Date_Debut', 'Date_Fin']
            for col in date_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_datetime(self.df_clean[col], errors='coerce')

            # Calculer des métriques dérivées
            if 'Date_Debut' in self.df_clean.columns and 'Date_Fin' in self.df_clean.columns:
                self.df_clean['Duree_Prevue'] = (self.df_clean['Date_Fin'] - self.df_clean['Date_Debut']).dt.days
                self.df_clean['Jours_Restants'] = (self.df_clean['Date_Fin'] - pd.Timestamp.now()).dt.days

            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Budget'] > 0) &
                (self.df_clean['Nom_Projet'] != '') &
                (self.df_clean['Nom_Projet'] != 'nan')
            ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des projets (déjà dans df_clean)
        self.df_projets = self.df_clean.copy()

        # DataFrame des statuts
        if not self.df_clean.empty:
            self.df_statuts = self.df_clean.groupby('Statut').agg({
                'Budget': ['sum', 'mean', 'count'],
                'Avancement': 'mean'
            }).round(2)
            self.df_statuts.columns = ['Budget_Total', 'Budget_Moyen', 'Nombre_Projets', 'Avancement_Moyen']
            self.df_statuts = self.df_statuts.reset_index()

            # DataFrame des budgets (analyse par tranche)
            self.df_budgets = self.df_clean.copy()
            if 'Budget' in self.df_budgets.columns:
                # Créer des tranches de budget
                self.df_budgets['Tranche_Budget'] = pd.cut(
                    self.df_budgets['Budget'],
                    bins=[0, 10000000, 50000000, 100000000, float('inf')],
                    labels=['< 10M', '10M-50M', '50M-100M', '> 100M']
                )

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES G-PROJET")
        print("="*80)

        # Résumé général
        budget_total = self.df_clean['Budget'].sum()
        avancement_moyen = self.df_clean['Avancement'].mean()

        print(f"\nRésumé général:")
        print(f"- Nombre de projets: {len(self.df_clean)}")
        print(f"- Budget total: {budget_total:,.0f} FCFA")
        print(f"- Budget moyen: {self.df_clean['Budget'].mean():,.0f} FCFA")
        print(f"- Avancement moyen: {avancement_moyen:.1f}%")
        print(f"- Statuts différents: {self.df_clean['Statut'].nunique()}")

        # Affichage des projets
        print(f"\nPROJETS ({len(self.df_projets)} éléments)")
        print("-" * 50)
        print(self.df_projets.to_string())

        # Affichage des statuts
        if hasattr(self, 'df_statuts') and not self.df_statuts.empty:
            print(f"\nANALYSE PAR STATUT ({len(self.df_statuts)} éléments)")
            print("-" * 50)
            print(self.df_statuts.to_string())

        # Affichage des budgets par tranche
        if hasattr(self, 'df_budgets') and 'Tranche_Budget' in self.df_budgets.columns:
            print(f"\nRÉPARTITION PAR TRANCHE DE BUDGET")
            print("-" * 50)
            repartition_budget = self.df_budgets.groupby('Tranche_Budget').agg({
                'Budget': ['count', 'sum'],
                'Avancement': 'mean'
            }).round(2)
            repartition_budget.columns = ['Nombre_Projets', 'Budget_Total', 'Avancement_Moyen']
            print(repartition_budget.to_string())

        # Projets en retard ou à risque
        if 'Jours_Restants' in self.df_clean.columns:
            projets_risque = self.df_clean[
                (self.df_clean['Jours_Restants'] < 30) &
                (self.df_clean['Avancement'] < 80)
            ]
            if not projets_risque.empty:
                print(f"\nPROJETS À RISQUE ({len(projets_risque)} éléments)")
                print("-" * 50)
                print(projets_risque[['Nom_Projet', 'Avancement', 'Jours_Restants', 'Budget']].to_string())


class GLocativeAnalytics:
    """
    Classe d'analyse pour les données G-Locative
    Gère le chargement, nettoyage et affichage des données de gestion locative
    """

    def __init__(self):
        self.model = GLocative
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_biens = None
        self.df_proprietaires = None
        self.df_locataires = None
        self.df_charges = None
        self.df_impayés = None

    def load_data(self):
        """
        Charge les données G-Locative depuis la base de données
        """
        try:
            latest_locative = self.model.objects.order_by('-created_at').first()

            if not latest_locative:
                print("Aucune donnée G-Locative trouvée")
                return False

            self.data = latest_locative.data
            print(f"Données G-Locative chargées: {latest_locative.title}")
            print(f"Date de création: {latest_locative.created_at}")

            if isinstance(self.data, list):
                self._parse_locative_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_locative_structure(self):
        """
        Parse la structure des données de gestion locative
        """
        try:
            all_biens = []

            for bien in self.data:
                # Données de base du bien
                bien_data = {
                    'ID': bien.get('id', 0),
                    'Libelle': bien.get('libelle', ''),
                    'Adresse': bien.get('adresse', ''),
                    'Type_ID': bien.get('type_id', 0),
                    'Type_Libelle': bien.get('type', {}).get('libelle', ''),
                    'Total_Charges': bien.get('totalCharges', 0),
                    'Total_Loyer': bien.get('totaLoyer', 0),
                    'Total_Impaye': bien.get('totalImpayer', 0),
                    'Nombre_Proprietaires': len(bien.get('proprietaires', [])),
                    'Nombre_Locataires': len(bien.get('locataires', [])),
                    'Nombre_Charges': len(bien.get('charges', [])),
                    'Nombre_Actifs': len(bien.get('actifs', []))
                }

                # Calculer des métriques dérivées
                bien_data['Rentabilite_Brute'] = bien_data['Total_Loyer'] / max(bien_data['Total_Charges'], 1) if bien_data['Total_Charges'] > 0 else 0
                bien_data['Taux_Impaye'] = (bien_data['Total_Impaye'] / max(bien_data['Total_Loyer'], 1)) * 100 if bien_data['Total_Loyer'] > 0 else 0
                bien_data['Statut_Occupation'] = 'Occupé' if bien_data['Nombre_Locataires'] > 0 else 'Vacant'

                all_biens.append(bien_data)

            self.df_raw = pd.DataFrame(all_biens)
            print(f"Structure parsée: {len(all_biens)} biens extraits")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données de gestion locative
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            numeric_columns = ['ID', 'Type_ID', 'Total_Charges', 'Total_Loyer', 'Total_Impaye',
                             'Nombre_Proprietaires', 'Nombre_Locataires', 'Nombre_Charges',
                             'Nombre_Actifs', 'Rentabilite_Brute', 'Taux_Impaye']

            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Libelle', 'Adresse', 'Type_Libelle', 'Statut_Occupation']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()

            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Libelle'] != '') &
                (self.df_clean['Libelle'] != 'nan')
            ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} biens valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des biens (déjà dans df_clean)
        self.df_biens = self.df_clean.copy()

        # DataFrame par type de bien
        if not self.df_clean.empty:
            self.df_types = self.df_clean.groupby('Type_Libelle').agg({
                'Total_Loyer': ['sum', 'mean', 'count'],
                'Total_Impaye': 'sum',
                'Total_Charges': 'sum',
                'Taux_Impaye': 'mean'
            }).round(2)
            self.df_types.columns = ['Loyer_Total', 'Loyer_Moyen', 'Nombre_Biens', 'Impaye_Total', 'Charges_Total', 'Taux_Impaye_Moyen']
            self.df_types = self.df_types.reset_index()

            # DataFrame des biens avec impayés
            self.df_impayés = self.df_clean[self.df_clean['Total_Impaye'] > 0].copy()
            if not self.df_impayés.empty:
                self.df_impayés = self.df_impayés.sort_values('Total_Impaye', ascending=False)

            # DataFrame par statut d'occupation
            self.df_occupation = self.df_clean.groupby('Statut_Occupation').agg({
                'Total_Loyer': ['sum', 'count'],
                'Total_Impaye': 'sum'
            }).round(2)
            self.df_occupation.columns = ['Loyer_Total', 'Nombre_Biens', 'Impaye_Total']
            self.df_occupation = self.df_occupation.reset_index()

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES G-LOCATIVE")
        print("="*80)

        # Résumé général
        loyer_total = self.df_clean['Total_Loyer'].sum()
        impaye_total = self.df_clean['Total_Impaye'].sum()
        taux_impaye_global = (impaye_total / loyer_total * 100) if loyer_total > 0 else 0

        print(f"\nRésumé général:")
        print(f"- Nombre de biens: {len(self.df_clean)}")
        print(f"- Loyer total: {loyer_total:,.0f} FCFA")
        print(f"- Impayés total: {impaye_total:,.0f} FCFA")
        print(f"- Taux d'impayés global: {taux_impaye_global:.1f}%")
        print(f"- Biens occupés: {len(self.df_clean[self.df_clean['Statut_Occupation'] == 'Occupé'])}")
        print(f"- Biens vacants: {len(self.df_clean[self.df_clean['Statut_Occupation'] == 'Vacant'])}")

        # Affichage des biens
        print(f"\nBIENS IMMOBILIERS ({len(self.df_biens)} éléments)")
        print("-" * 50)
        colonnes_affichage = ['Libelle', 'Type_Libelle', 'Total_Loyer', 'Total_Impaye', 'Statut_Occupation']
        print(self.df_biens[colonnes_affichage].head(10).to_string())

        # Affichage par type de bien
        if hasattr(self, 'df_types') and not self.df_types.empty:
            print(f"\nANALYSE PAR TYPE DE BIEN ({len(self.df_types)} éléments)")
            print("-" * 50)
            print(self.df_types.to_string())

        # Affichage des impayés
        if hasattr(self, 'df_impayés') and not self.df_impayés.empty:
            print(f"\nBIENS AVEC IMPAYÉS ({len(self.df_impayés)} éléments)")
            print("-" * 50)
            colonnes_impayés = ['Libelle', 'Total_Loyer', 'Total_Impaye', 'Taux_Impaye']
            print(self.df_impayés[colonnes_impayés].head(10).to_string())

        # Affichage par statut d'occupation
        if hasattr(self, 'df_occupation') and not self.df_occupation.empty:
            print(f"\nANALYSE PAR STATUT D'OCCUPATION")
            print("-" * 50)
            print(self.df_occupation.to_string())

        # Top 5 des biens les plus rentables
        if 'Rentabilite_Brute' in self.df_clean.columns:
            top_rentables = self.df_clean.nlargest(5, 'Rentabilite_Brute')[['Libelle', 'Total_Loyer', 'Total_Charges', 'Rentabilite_Brute']]
            if not top_rentables.empty:
                print(f"\nTOP 5 BIENS LES PLUS RENTABLES")
                print("-" * 50)
                print(top_rentables.to_string())


class G_LOCATIVEAnalyticsEngine:
    """
    Placeholder pour G_LOCATIVEAnalyticsEngine
    À implémenter plus tard
    """
    pass


class AnalyticsEngine:
    """
    Core analytics engine for processing KAYDAN real estate data
    """

    def __init__(self):
        self.alert_thresholds = {
            "budget_variance": 10.0,  # 10% variance threshold
            "schedule_delay": 7,  # 7 days delay threshold
            "stock_shortage": 20.0,  # 20% stock level threshold
        }

    def run_complete_analysis(self):
        """
        Run complete analytics pipeline
        """
        results = {
            "project_analytics": self.analyze_projects(),
            "stock_analytics": self.analyze_stock_performance(),
            "business_kpis": self.calculate_business_kpis(),
            "alerts_generated": self.generate_alerts(),
            "analysis_timestamp": timezone.now(),
        }
        return results

    def analyze_projects(self):
        """
        Analyze project performance combining DQE, G-Projet, and G-Stock data
        """
        project_analytics = []

        # Get latest data from each source
        latest_dqe = DQEData.objects.order_by("-created_at").first()
        latest_gprojet = GProjet.objects.order_by("-created_at").first()

        if latest_dqe and latest_gprojet:
            # Process DQE data for financial metrics
            dqe_data = latest_dqe.data
            gprojet_data = latest_gprojet.data

            # Extract projects from data
            projects = self._extract_projects_data(dqe_data, gprojet_data)

            for project in projects:
                analytics = self._calculate_project_metrics(project)

                # Save to database
                project_analytics_obj, created = (
                    ProjectAnalytics.objects.update_or_create(
                        project_name=analytics["project_name"], defaults=analytics
                    )
                )

                project_analytics.append(analytics)

        return project_analytics

    def analyze_stock_performance(self):
        """
        Analyze stock management efficiency
        """
        # Get latest stock data
        latest_appro = GStockApprovisionnement.objects.order_by("-created_at").first()
        latest_sortie = GStockSortie.objects.order_by("-created_at").first()
        latest_conso = GStockConsommation.objects.order_by("-created_at").first()
        latest_achat = GStockAchat.objects.order_by("-created_at").first()

        if all([latest_appro, latest_sortie, latest_conso, latest_achat]):
            stock_metrics = self._calculate_stock_metrics(
                latest_appro.data,
                latest_sortie.data,
                latest_conso.data,
                latest_achat.data,
            )

            # Save to database
            period_start = timezone.now().date().replace(day=1)
            period_end = timezone.now().date()

            stock_analytics, created = StockAnalytics.objects.update_or_create(
                analysis_period="monthly",
                period_start=period_start,
                period_end=period_end,
                defaults=stock_metrics,
            )

            return stock_metrics

        return {}

    def calculate_business_kpis(self):
        """
        Calculate high-level business KPIs
        """
        today = timezone.now().date()

        # Aggregate data from recent analytics
        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=today - timedelta(days=30)
        )

        recent_stock = StockAnalytics.objects.filter(
            period_end__gte=today - timedelta(days=30)
        ).first()

        kpis = {
            "project_completion_rate": self._calculate_completion_rate(recent_projects),
            "average_budget_variance": self._calculate_avg_budget_variance(
                recent_projects
            ),
            "stock_efficiency_score": self._calculate_stock_efficiency(recent_stock),
            "overall_performance_score": 0.0,  # Calculated below
        }

        # Calculate overall performance score
        kpis["overall_performance_score"] = (
            kpis["project_completion_rate"] * 0.4
            + (100 - abs(kpis["average_budget_variance"])) * 0.3
            + kpis["stock_efficiency_score"] * 0.3
        )

        # Save to database
        business_kpi, created = BusinessKPIs.objects.update_or_create(
            kpi_date=today,
            kpi_type="daily",
            defaults={
                "project_completion_rate": kpis["project_completion_rate"],
                "average_project_delay": kpis["average_budget_variance"],
                "kpi_data": kpis,
            },
        )

        return kpis

    def generate_alerts(self):
        """
        Generate automated alerts based on thresholds
        """
        alerts_created = []

        # Check for budget overruns
        budget_alerts = self._check_budget_alerts()
        alerts_created.extend(budget_alerts)

        # Check for stock shortages
        stock_alerts = self._check_stock_alerts()
        alerts_created.extend(stock_alerts)

        # Check for schedule delays
        schedule_alerts = self._check_schedule_alerts()
        alerts_created.extend(schedule_alerts)

        return alerts_created

    # Helper methods
    def _extract_projects_data(self, dqe_data, gprojet_data):
        """Extract and combine project data from different sources"""
        projects = []

        # This would depend on the actual structure of your API data
        # You'll need to adapt this based on your real data format

        # Example structure - adapt to your actual data
        if isinstance(dqe_data, dict) and "projects" in dqe_data:
            for project in dqe_data["projects"]:
                project_info = {
                    "name": project.get("name", "Unknown Project"),
                    "estimated_budget": project.get("estimated_cost", 0),
                    "dqe_data": project,
                }

                # Find corresponding G-Projet data
                gprojet_info = self._find_project_in_gprojet(
                    project_info["name"], gprojet_data
                )
                if gprojet_info:
                    project_info["gprojet_data"] = gprojet_info

                projects.append(project_info)

        return projects

    def _calculate_project_metrics(self, project_data):
        """Calculate analytics metrics for a single project"""
        metrics = {
            "project_name": project_data["name"],
            "estimated_budget": Decimal(str(project_data.get("estimated_budget", 0))),
            "data_sources": ["DQE", "G-Projet"],
        }

        # Calculate budget variance if actual cost is available
        if "actual_cost" in project_data:
            actual_cost = Decimal(str(project_data["actual_cost"]))
            metrics["actual_cost"] = actual_cost
            metrics["budget_variance"] = actual_cost - metrics["estimated_budget"]

            if metrics["estimated_budget"] > 0:
                metrics["budget_variance_percentage"] = float(
                    (metrics["budget_variance"] / metrics["estimated_budget"]) * 100
                )

        # Calculate completion percentage from G-Projet data
        if "gprojet_data" in project_data:
            gprojet_info = project_data["gprojet_data"]
            metrics["completion_percentage"] = gprojet_info.get("completion_rate", 0.0)

            # Determine delivery performance
            if metrics["completion_percentage"] >= 100:
                metrics["delivery_performance"] = "Complete"
            elif metrics["completion_percentage"] >= 75:
                metrics["delivery_performance"] = "On-track"
            else:
                metrics["delivery_performance"] = "At-risk"

        return metrics

    def _calculate_stock_metrics(self, appro_data, sortie_data, conso_data, achat_data):
        """Calculate stock performance metrics"""
        metrics = {
            "total_stock_value": Decimal("0"),
            "stock_turnover_ratio": 0.0,
            "stockout_incidents": 0,
            "materials_performance": {},
        }

        # Process stock data based on your actual API structure
        # This is a template - adapt to your real data format

        if isinstance(appro_data, dict) and "total_value" in appro_data:
            metrics["total_stock_value"] = Decimal(str(appro_data["total_value"]))

        if isinstance(sortie_data, dict) and "turnover_rate" in sortie_data:
            metrics["stock_turnover_ratio"] = float(sortie_data["turnover_rate"])

        return metrics

    def _check_budget_alerts(self):
        """Check for budget-related alerts"""
        alerts = []

        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=timezone.now() - timedelta(days=7),
            budget_variance_percentage__isnull=False,
        )

        for project in recent_projects:
            if (
                abs(project.budget_variance_percentage)
                > self.alert_thresholds["budget_variance"]
            ):
                severity = (
                    "high" if abs(project.budget_variance_percentage) > 20 else "medium"
                )

                alert = AlertSystem.objects.create(
                    alert_type="budget_overrun",
                    severity=severity,
                    title=f"Budget variance detected for {project.project_name}",
                    message=f"Project {project.project_name} has a budget variance of {project.budget_variance_percentage:.1f}%",
                    project_name=project.project_name,
                    threshold_value=self.alert_thresholds["budget_variance"],
                    actual_value=abs(project.budget_variance_percentage),
                    variance_percentage=project.budget_variance_percentage,
                )
                alerts.append(alert)

        return alerts

    def _check_stock_alerts(self):
        """Check for stock-related alerts"""
        alerts = []

        # Get latest stock data and check for shortages
        latest_conso = GStockConsommation.objects.order_by("-created_at").first()

        if latest_conso and isinstance(latest_conso.data, dict):
            # Analyze consumption data for potential shortages
            # This depends on your actual data structure
            consumption_data = latest_conso.data

            # Example logic - adapt to your data
            if "stock_levels" in consumption_data:
                for item, level in consumption_data["stock_levels"].items():
                    if level < self.alert_thresholds["stock_shortage"]:
                        alert = AlertSystem.objects.create(
                            alert_type="stock_shortage",
                            severity="medium",
                            title=f"Low stock alert for {item}",
                            message=f"Stock level for {item} is at {level}%, below threshold",
                            affected_area="Stock Management",
                            threshold_value=self.alert_thresholds["stock_shortage"],
                            actual_value=level,
                        )
                        alerts.append(alert)

        return alerts

    def _check_schedule_alerts(self):
        """Check for schedule-related alerts"""
        alerts = []

        # Check projects for schedule delays
        at_risk_projects = ProjectAnalytics.objects.filter(
            delivery_performance="At-risk",
            analysis_date__gte=timezone.now() - timedelta(days=7),
        )

        for project in at_risk_projects:
            alert = AlertSystem.objects.create(
                alert_type="schedule_delay",
                severity="medium",
                title=f"Schedule risk for {project.project_name}",
                message=f"Project {project.project_name} is at risk of schedule delays",
                project_name=project.project_name,
                actual_value=project.completion_percentage or 0,
            )
            alerts.append(alert)

        return alerts
