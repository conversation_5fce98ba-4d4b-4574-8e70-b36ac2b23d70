# analytics_engine.py
import json
import pandas as pd
from decimal import Decimal
from datetime import datetime, timedelta
from django.db.models import Q, Avg, Sum, Count
from django.utils import timezone
from api.models import (
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
)
from .models import (
    ProjectAnalytics,
    StockAnalytics,
    BusinessKPIs,
    AlertSystem,
)


class DQEAnalytics:
    """
    Classe d'analyse pour les données DQE (Devis Quantitatif Estimatif)
    Gère le chargement, nettoyage et affichage des données DQE
    """

    def __init__(self):
        self.model = DQEData
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_materiaux = None
        self.df_main_oeuvre = None
        self.df_prix_unitaires = None
        self.df_totaux_categories = None

    def load_data(self):
        """
        Charge les données DQE depuis la base de données
        """
        try:
            # Récupérer la dernière entrée DQE
            latest_dqe = self.model.objects.order_by('-created_at').first()

            if not latest_dqe:
                print("Aucune donnée DQE trouvée dans la base de données")
                return False

            self.data = latest_dqe.data
            print(f"Données DQE chargées avec succès: {latest_dqe.title}")
            print(f"Date de création: {latest_dqe.created_at}")

            # Convertir en DataFrame pour traitement initial
            if isinstance(self.data, dict) and 'data' in self.data:
                self._parse_dqe_structure()
                return True
            else:
                print("Structure de données DQE invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement des données DQE: {str(e)}")
            return False

    def _parse_dqe_structure(self):
        """
        Parse la structure complexe des données DQE
        """
        try:
            dqe_data = self.data['data']
            all_items = []

            # Parcourir la structure hiérarchique
            for program_data in dqe_data:
                program_name = program_data.get('program', {}).get('name', 'Programme Inconnu')

                for actif in program_data.get('actifs', []):
                    actif_info = actif.get('actif', {})
                    type_actif = actif_info.get('type_actif', {}).get('name', 'Type Inconnu')

                    for corps_etat in actif.get('corps_etats', []):
                        corps_etat_name = corps_etat.get('corps_etat', {}).get('name', 'Corps État Inconnu')

                        # Extraire récursivement tous les items
                        self._extract_items_recursive(
                            corps_etat.get('dataDqe', []),
                            all_items,
                            program_name,
                            type_actif,
                            corps_etat_name
                        )

            # Créer le DataFrame brut
            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure DQE parsée: {len(all_items)} éléments extraits")

        except Exception as e:
            print(f"Erreur lors du parsing de la structure DQE: {str(e)}")

    def _extract_items_recursive(self, items, all_items, program_name, type_actif, corps_etat_name, parent_lot=""):
        """
        Extrait récursivement tous les items de la structure DQE
        """
        for item in items:
            if 'items' in item and item['items']:
                # Item avec sous-items - continuer récursivement
                current_lot = f"{parent_lot}.{item.get('lot', '')}" if parent_lot else item.get('lot', '')
                self._extract_items_recursive(
                    item['items'],
                    all_items,
                    program_name,
                    type_actif,
                    corps_etat_name,
                    current_lot
                )
            else:
                # Item final - extraire les données
                item_data = {
                    'Programme': program_name,
                    'Type_Actif': type_actif,
                    'Corps_Etat': corps_etat_name,
                    'Lot': f"{parent_lot}.{item.get('lot', '')}" if parent_lot else item.get('lot', ''),
                    'ID': item.get('id'),
                    'Nom': item.get('nom', item.get('name', '')),
                    'Code': item.get('code', ''),
                    'Type': item.get('type', {}).get('name', ''),
                    'Quantite': item.get('quantite', 0),
                    'Base_Qty': item.get('base_qty', 0),
                    'PU_Total': item.get('pu_total', 0),
                    'PT_Total': item.get('pt_total', 0),
                    'PU_Materiel': item.get('pu_materiel', 0),
                    'PT_Materiel': item.get('pt_materiel', 0),
                    'PU_Main_Oeuvre': item.get('pu_main_oeuvre', 0),
                    'PT_Main_Oeuvre': item.get('pt_main_oeuvre', 0),
                    'PU_Coef_V': item.get('pu_coef_v', 0),
                    'PT_Coef_V': item.get('pt_coef_v', 0),
                    'Status': item.get('status', 0),
                    'Created_At': item.get('created_at', ''),
                    'Updated_At': item.get('updated_at', '')
                }

                # Ajouter les informations de prix si disponibles
                if 'prices' in item and item['prices']:
                    price_info = item['prices'][0]  # Prendre le premier prix
                    item_data['Gamme'] = price_info.get('gamme', {}).get('name', '')
                    item_data['Prix_Gamme'] = price_info.get('price', 0)

                # Ajouter les informations d'unité si disponibles
                if 'unites' in item and item['unites']:
                    unite_info = item['unites'][0]  # Prendre la première unité
                    item_data['Unite_Code'] = unite_info.get('code', '')
                    item_data['Unite_Name'] = unite_info.get('name', '')

                all_items.append(item_data)

    def clean_data(self):
        """
        Nettoie et formate les données DQE
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            # Copier les données brutes
            self.df_clean = self.df_raw.copy()

            # Supprimer les colonnes inutiles pour l'analyse
            columns_to_remove = ['ID', 'Created_At', 'Updated_At', 'Status']
            self.df_clean = self.df_clean.drop(columns=[col for col in columns_to_remove if col in self.df_clean.columns])

            # Nettoyer les valeurs numériques
            numeric_columns = ['Quantite', 'Base_Qty', 'PU_Total', 'PT_Total', 'PU_Materiel',
                             'PT_Materiel', 'PU_Main_Oeuvre', 'PT_Main_Oeuvre', 'PU_Coef_V',
                             'PT_Coef_V', 'Prix_Gamme']

            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Programme', 'Type_Actif', 'Corps_Etat', 'Nom', 'Code', 'Type', 'Gamme', 'Unite_Code', 'Unite_Name']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip()
                    # Première lettre en majuscule pour les noms
                    if col in ['Nom', 'Type', 'Gamme']:
                        self.df_clean[col] = self.df_clean[col].str.title()

            # Filtrer les lignes avec des données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Nom'] != '') &
                (self.df_clean['Nom'] != 'nan') &
                (self.df_clean['PT_Total'] > 0)
            ]

            # Créer les DataFrames spécialisés
            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage des données: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des matériaux
        self.df_materiaux = self.df_clean[self.df_clean['Type'] == 'Materiel'].copy()
        if not self.df_materiaux.empty:
            self.df_materiaux = self.df_materiaux[['Programme', 'Corps_Etat', 'Nom', 'Code', 'Quantite',
                                                 'Unite_Name', 'PU_Materiel', 'PT_Materiel', 'Gamme']].copy()

        # DataFrame de la main d'œuvre
        self.df_main_oeuvre = self.df_clean[self.df_clean['PT_Main_Oeuvre'] > 0].copy()
        if not self.df_main_oeuvre.empty:
            self.df_main_oeuvre = self.df_main_oeuvre[['Programme', 'Corps_Etat', 'Nom', 'Quantite',
                                                     'PU_Main_Oeuvre', 'PT_Main_Oeuvre']].copy()

        # DataFrame des prix unitaires
        self.df_prix_unitaires = self.df_clean[['Programme', 'Corps_Etat', 'Nom', 'Type',
                                              'PU_Total', 'Unite_Name', 'Gamme']].copy()

        # DataFrame des totaux par catégorie
        self.df_totaux_categories = self.df_clean.groupby(['Programme', 'Corps_Etat', 'Type']).agg({
            'PT_Total': 'sum',
            'PT_Materiel': 'sum',
            'PT_Main_Oeuvre': 'sum',
            'PT_Coef_V': 'sum',
            'Quantite': 'count'
        }).reset_index()

        self.df_totaux_categories.columns = ['Programme', 'Corps_Etat', 'Type', 'Total_Prix',
                                           'Total_Materiel', 'Total_Main_Oeuvre', 'Total_Coef_V', 'Nombre_Items']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES DQE")
        print("="*80)

        # Résumé général
        print(f"\nRésumé général:")
        print(f"- Nombre total d'éléments: {len(self.df_clean):,}")
        print(f"- Programmes: {self.df_clean['Programme'].nunique()}")
        print(f"- Corps d'état: {self.df_clean['Corps_Etat'].nunique()}")
        print(f"- Types d'éléments: {self.df_clean['Type'].nunique()}")
        print(f"- Montant total: {self.df_clean['PT_Total'].sum():,.0f} FCFA")

        # Affichage des matériaux
        if not self.df_materiaux.empty:
            print(f"\n📦 MATÉRIAUX ({len(self.df_materiaux)} éléments)")
            print("-" * 50)
            print(self.df_materiaux.head(10).to_string())

            print(f"\nTop 5 matériaux par coût:")
            top_materiaux = self.df_materiaux.nlargest(5, 'PT_Materiel')[['Nom', 'PT_Materiel', 'Quantite', 'Unite_Name']]
            print(top_materiaux.to_string())

        # Affichage de la main d'œuvre
        if not self.df_main_oeuvre.empty:
            print(f"\n👷 MAIN D'ŒUVRE ({len(self.df_main_oeuvre)} éléments)")
            print("-" * 50)
            print(self.df_main_oeuvre.head(10).to_string())

            print(f"\nTop 5 postes main d'œuvre par coût:")
            top_main_oeuvre = self.df_main_oeuvre.nlargest(5, 'PT_Main_Oeuvre')[['Nom', 'PT_Main_Oeuvre', 'Quantite']]
            print(top_main_oeuvre.to_string())

        # Affichage des prix unitaires
        print(f"\n💰 PRIX UNITAIRES ({len(self.df_prix_unitaires)} éléments)")
        print("-" * 50)
        print(self.df_prix_unitaires.head(10).to_string())

        # Affichage des totaux par catégorie
        print(f"\n📊 TOTAUX PAR CATÉGORIE")
        print("-" * 50)
        print(self.df_totaux_categories.to_string())

        # Répartition par programme
        print(f"\n🏗️ RÉPARTITION PAR PROGRAMME")
        print("-" * 50)
        repartition_programme = self.df_clean.groupby('Programme')['PT_Total'].sum().sort_values(ascending=False)
        for programme, total in repartition_programme.items():
            pourcentage = (total / self.df_clean['PT_Total'].sum()) * 100
            print(f"  {programme}: {total:,.0f} FCFA ({pourcentage:.1f}%)")

        # Répartition par corps d'état
        print(f"\n🔧 RÉPARTITION PAR CORPS D'ÉTAT")
        print("-" * 50)
        repartition_corps = self.df_clean.groupby('Corps_Etat')['PT_Total'].sum().sort_values(ascending=False)
        for corps, total in repartition_corps.items():
            pourcentage = (total / self.df_clean['PT_Total'].sum()) * 100
            print(f"  {corps}: {total:,.0f} FCFA ({pourcentage:.1f}%)")


class GStockApprovisionnementAnalytics:
    """
    Classe d'analyse pour les données G-Stock Approvisionnement
    Gère le chargement, nettoyage et affichage des données d'approvisionnement
    """

    def __init__(self):
        self.model = GStockApprovisionnement
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_approvisionnements = None
        self.df_produits = None
        self.df_fournisseurs = None
        self.df_programmes = None

    def load_data(self):
        """
        Charge les données G-Stock Approvisionnement depuis la base de données
        """
        try:
            latest_appro = self.model.objects.order_by('-created_at').first()

            if not latest_appro:
                print("Aucune donnée G-Stock Approvisionnement trouvée")
                return False

            self.data = latest_appro.data
            print(f"Données G-Stock Approvisionnement chargées: {latest_appro.title}")
            print(f"Date de création: {latest_appro.created_at}")

            if isinstance(self.data, dict) and 'stocks' in self.data:
                self._parse_approvisionnement_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_approvisionnement_structure(self):
        """
        Parse la structure des données d'approvisionnement
        """
        try:
            all_items = []

            for stock in self.data.get('stocks', []):
                programme_info = stock.get('programme', {})
                programme_nom = programme_info.get('nom', 'Programme Inconnu')
                programme_id = programme_info.get('id', 0)

                for appro in stock.get('approvisionnements', []):
                    appro_id = appro.get('id', 0)
                    ref = appro.get('ref', '')
                    created_at = appro.get('created_at', '')
                    fournisseur_id = appro.get('fournisseur_id', 0)

                    for item in appro.get('items', []):
                        produit_info = item.get('produits', {})

                        item_data = {
                            'Programme_ID': programme_id,
                            'Programme_Nom': programme_nom,
                            'Approvisionnement_ID': appro_id,
                            'Reference': ref,
                            'Date_Creation': created_at,
                            'Fournisseur_ID': fournisseur_id,
                            'Item_ID': item.get('id', 0),
                            'Produit_ID': item.get('produit_id', 0),
                            'Produit_Code': produit_info.get('code', ''),
                            'Produit_Libelle': produit_info.get('libelle', ''),
                            'Quantite': item.get('qte', 0)
                        }

                        all_items.append(item_data)

            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure parsée: {len(all_items)} éléments extraits")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données d'approvisionnement
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            numeric_columns = ['Programme_ID', 'Approvisionnement_ID', 'Fournisseur_ID',
                             'Item_ID', 'Produit_ID', 'Quantite']

            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            string_columns = ['Programme_Nom', 'Reference', 'Produit_Code', 'Produit_Libelle']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip()
                    if col in ['Programme_Nom', 'Produit_Libelle']:
                        self.df_clean[col] = self.df_clean[col].str.title()

            # Convertir les dates
            if 'Date_Creation' in self.df_clean.columns:
                self.df_clean['Date_Creation'] = pd.to_datetime(self.df_clean['Date_Creation'], errors='coerce')

            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Quantite'] > 0) &
                (self.df_clean['Produit_Libelle'] != '') &
                (self.df_clean['Produit_Libelle'] != 'nan')
            ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des approvisionnements
        self.df_approvisionnements = self.df_clean.groupby(['Approvisionnement_ID', 'Reference', 'Programme_Nom', 'Date_Creation']).agg({
            'Quantite': 'sum',
            'Produit_ID': 'count'
        }).reset_index()
        self.df_approvisionnements.columns = ['Approvisionnement_ID', 'Reference', 'Programme', 'Date_Creation', 'Quantite_Totale', 'Nombre_Produits']

        # DataFrame des produits
        self.df_produits = self.df_clean.groupby(['Produit_Code', 'Produit_Libelle']).agg({
            'Quantite': 'sum',
            'Approvisionnement_ID': 'nunique'
        }).reset_index()
        self.df_produits.columns = ['Code_Produit', 'Libelle_Produit', 'Quantite_Totale', 'Nombre_Approvisionnements']

        # DataFrame des fournisseurs
        self.df_fournisseurs = self.df_clean.groupby('Fournisseur_ID').agg({
            'Quantite': 'sum',
            'Approvisionnement_ID': 'nunique',
            'Produit_ID': 'nunique'
        }).reset_index()
        self.df_fournisseurs.columns = ['Fournisseur_ID', 'Quantite_Totale', 'Nombre_Approvisionnements', 'Nombre_Produits']

        # DataFrame des programmes
        self.df_programmes = self.df_clean.groupby(['Programme_ID', 'Programme_Nom']).agg({
            'Quantite': 'sum',
            'Approvisionnement_ID': 'nunique',
            'Fournisseur_ID': 'nunique'
        }).reset_index()
        self.df_programmes.columns = ['Programme_ID', 'Programme_Nom', 'Quantite_Totale', 'Nombre_Approvisionnements', 'Nombre_Fournisseurs']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES G-STOCK APPROVISIONNEMENT")
        print("="*80)

        # Résumé général
        print(f"\nRésumé général:")
        print(f"- Nombre total d'éléments: {len(self.df_clean):,}")
        print(f"- Programmes: {self.df_clean['Programme_Nom'].nunique()}")
        print(f"- Approvisionnements: {self.df_clean['Approvisionnement_ID'].nunique()}")
        print(f"- Fournisseurs: {self.df_clean['Fournisseur_ID'].nunique()}")
        print(f"- Produits différents: {self.df_clean['Produit_ID'].nunique()}")
        print(f"- Quantité totale: {self.df_clean['Quantite'].sum():,.0f}")

        # Affichage des approvisionnements
        if not self.df_approvisionnements.empty:
            print(f"\nAPPROVISIONNEMENTS ({len(self.df_approvisionnements)} éléments)")
            print("-" * 50)
            print(self.df_approvisionnements.head(10).to_string())

            print(f"\nTop 5 approvisionnements par quantité:")
            top_appro = self.df_approvisionnements.nlargest(5, 'Quantite_Totale')[['Reference', 'Programme', 'Quantite_Totale', 'Nombre_Produits']]
            print(top_appro.to_string())

        # Affichage des produits
        if not self.df_produits.empty:
            print(f"\nPRODUITS ({len(self.df_produits)} éléments)")
            print("-" * 50)
            print(self.df_produits.head(10).to_string())

            print(f"\nTop 5 produits par quantité:")
            top_produits = self.df_produits.nlargest(5, 'Quantite_Totale')[['Code_Produit', 'Libelle_Produit', 'Quantite_Totale']]
            print(top_produits.to_string())

        # Affichage des fournisseurs
        if not self.df_fournisseurs.empty:
            print(f"\nFOURNISSEURS ({len(self.df_fournisseurs)} éléments)")
            print("-" * 50)
            print(self.df_fournisseurs.head(10).to_string())

            print(f"\nTop 5 fournisseurs par quantité:")
            top_fournisseurs = self.df_fournisseurs.nlargest(5, 'Quantite_Totale')[['Fournisseur_ID', 'Quantite_Totale', 'Nombre_Approvisionnements']]
            print(top_fournisseurs.to_string())

        # Affichage des programmes
        if not self.df_programmes.empty:
            print(f"\nPROGRAMMES ({len(self.df_programmes)} éléments)")
            print("-" * 50)
            print(self.df_programmes.to_string())

        # Répartition par programme
        print(f"\nRÉPARTITION PAR PROGRAMME")
        print("-" * 50)
        repartition_programme = self.df_clean.groupby('Programme_Nom')['Quantite'].sum().sort_values(ascending=False)
        for programme, total in repartition_programme.items():
            pourcentage = (total / self.df_clean['Quantite'].sum()) * 100
            print(f"  {programme}: {total:,.0f} unités ({pourcentage:.1f}%)")


class EcoleTalentsAnalytics:
    """
    Classe d'analyse pour les données École des Talents
    Gère le chargement, nettoyage et affichage des données de formation
    """

    def __init__(self):
        self.model = EcoleTalents
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_promotions = None
        self.df_inscriptions = None
        self.df_metiers = None

    def load_data(self):
        """
        Charge les données École des Talents depuis la base de données
        """
        try:
            latest_ecole = self.model.objects.order_by('-created_at').first()

            if not latest_ecole:
                print("Aucune donnée École des Talents trouvée")
                return False

            self.data = latest_ecole.data
            print(f"Données École des Talents chargées: {latest_ecole.title}")
            print(f"Date de création: {latest_ecole.created_at}")

            if isinstance(self.data, dict) and 'data' in self.data:
                self._parse_ecole_structure()
                return True
            else:
                print("Structure de données invalide")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_ecole_structure(self):
        """
        Parse la structure des données École des Talents
        """
        try:
            ecole_data = self.data['data']['data']
            all_promotions = []
            all_inscriptions = []

            for promotion in ecole_data:
                # Données de base de la promotion
                promo_data = {
                    'Promotion_ID': promotion.get('id', 0),
                    'Promotion_Nom': promotion.get('name', ''),
                    'Total_General': promotion.get('total_general', 0),
                    'Total_Eligible': promotion.get('total_eligible', 0),
                    'Total_NonEligible': promotion.get('total_noneligible', 0)
                }
                all_promotions.append(promo_data)

                # Inscriptions par métier
                for inscription in promotion.get('inscription_par_metier', []):
                    inscription_data = {
                        'Promotion_ID': promotion.get('id', 0),
                        'Promotion_Nom': promotion.get('name', ''),
                        'Metier': inscription.get('metier', ''),
                        'Total_Inscriptions': inscription.get('total', 0),
                        'Total_Eligible': inscription.get('eligible', 0),
                        'Total_NonEligible': inscription.get('noneligible', 0)
                    }
                    all_inscriptions.append(inscription_data)

            self.df_raw = pd.DataFrame(all_promotions)
            self.df_inscriptions_raw = pd.DataFrame(all_inscriptions)
            print(f"Structure parsée: {len(all_promotions)} promotions, {len(all_inscriptions)} inscriptions par métier")

        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        """
        Nettoie et formate les données École des Talents
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False

        try:
            # Nettoyer les données des promotions
            self.df_clean = self.df_raw.copy()

            # Nettoyer les valeurs numériques
            numeric_columns = ['Promotion_ID', 'Total_General', 'Total_Eligible', 'Total_NonEligible']
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)

            # Nettoyer les chaînes de caractères
            if 'Promotion_Nom' in self.df_clean.columns:
                self.df_clean['Promotion_Nom'] = self.df_clean['Promotion_Nom'].astype(str).str.strip().str.title()

            # Nettoyer les données des inscriptions
            if hasattr(self, 'df_inscriptions_raw') and not self.df_inscriptions_raw.empty:
                self.df_inscriptions = self.df_inscriptions_raw.copy()

                # Nettoyer les valeurs numériques des inscriptions
                numeric_cols_inscr = ['Promotion_ID', 'Total_Inscriptions', 'Total_Eligible', 'Total_NonEligible']
                for col in numeric_cols_inscr:
                    if col in self.df_inscriptions.columns:
                        self.df_inscriptions[col] = pd.to_numeric(self.df_inscriptions[col], errors='coerce').fillna(0)

                # Nettoyer les chaînes de caractères des inscriptions
                string_cols_inscr = ['Promotion_Nom', 'Metier']
                for col in string_cols_inscr:
                    if col in self.df_inscriptions.columns:
                        self.df_inscriptions[col] = self.df_inscriptions[col].astype(str).str.strip().str.title()

                # Filtrer les données valides
                self.df_inscriptions = self.df_inscriptions[
                    (self.df_inscriptions['Total_Inscriptions'] > 0) &
                    (self.df_inscriptions['Metier'] != '') &
                    (self.df_inscriptions['Metier'] != 'nan')
                ]

            self._create_specialized_dataframes()

            print(f"Données nettoyées: {len(self.df_clean)} promotions, {len(self.df_inscriptions) if hasattr(self, 'df_inscriptions') else 0} inscriptions")
            return True

        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des promotions (déjà dans df_clean)
        self.df_promotions = self.df_clean.copy()

        # DataFrame des métiers (agrégation des inscriptions par métier)
        if hasattr(self, 'df_inscriptions') and not self.df_inscriptions.empty:
            self.df_metiers = self.df_inscriptions.groupby('Metier').agg({
                'Total_Inscriptions': 'sum',
                'Total_Eligible': 'sum',
                'Total_NonEligible': 'sum',
                'Promotion_ID': 'nunique'
            }).reset_index()
            self.df_metiers.columns = ['Metier', 'Total_Inscriptions', 'Total_Eligible', 'Total_NonEligible', 'Nombre_Promotions']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("\n" + "="*80)
        print("ANALYSE DES DONNÉES ÉCOLE DES TALENTS")
        print("="*80)

        # Résumé général
        total_inscriptions = self.df_clean['Total_General'].sum()
        total_eligible = self.df_clean['Total_Eligible'].sum()
        taux_eligibilite = (total_eligible / total_inscriptions * 100) if total_inscriptions > 0 else 0

        print(f"\nRésumé général:")
        print(f"- Nombre de promotions: {len(self.df_clean)}")
        print(f"- Total inscriptions: {total_inscriptions:,}")
        print(f"- Total éligibles: {total_eligible:,}")
        print(f"- Taux d'éligibilité global: {taux_eligibilite:.1f}%")

        # Affichage des promotions
        print(f"\nPROMOTIONS ({len(self.df_promotions)} éléments)")
        print("-" * 50)
        print(self.df_promotions.to_string())

        # Affichage des inscriptions par métier
        if hasattr(self, 'df_inscriptions') and not self.df_inscriptions.empty:
            print(f"\nINSCRIPTIONS PAR MÉTIER ({len(self.df_inscriptions)} éléments)")
            print("-" * 50)
            print(self.df_inscriptions.head(10).to_string())

            print(f"\nTop 5 métiers par inscriptions:")
            top_metiers = self.df_inscriptions.nlargest(5, 'Total_Inscriptions')[['Metier', 'Total_Inscriptions', 'Total_Eligible', 'Promotion_Nom']]
            print(top_metiers.to_string())

        # Affichage des métiers agrégés
        if hasattr(self, 'df_metiers') and not self.df_metiers.empty:
            print(f"\nMÉTIERS AGRÉGÉS ({len(self.df_metiers)} éléments)")
            print("-" * 50)
            print(self.df_metiers.to_string())

        # Répartition par promotion
        print(f"\nRÉPARTITION PAR PROMOTION")
        print("-" * 50)
        for _, promo in self.df_promotions.iterrows():
            pourcentage = (promo['Total_General'] / total_inscriptions * 100) if total_inscriptions > 0 else 0
            taux_promo = (promo['Total_Eligible'] / promo['Total_General'] * 100) if promo['Total_General'] > 0 else 0
            print(f"  {promo['Promotion_Nom']}: {promo['Total_General']:,} inscriptions ({pourcentage:.1f}%) - {taux_promo:.1f}% éligibles")


class G_LOCATIVEAnalyticsEngine:
    """
    Placeholder pour G_LOCATIVEAnalyticsEngine
    À implémenter plus tard
    """
    pass


class AnalyticsEngine:
    """
    Core analytics engine for processing KAYDAN real estate data
    """

    def __init__(self):
        self.alert_thresholds = {
            "budget_variance": 10.0,  # 10% variance threshold
            "schedule_delay": 7,  # 7 days delay threshold
            "stock_shortage": 20.0,  # 20% stock level threshold
        }

    def run_complete_analysis(self):
        """
        Run complete analytics pipeline
        """
        results = {
            "project_analytics": self.analyze_projects(),
            "stock_analytics": self.analyze_stock_performance(),
            "business_kpis": self.calculate_business_kpis(),
            "alerts_generated": self.generate_alerts(),
            "analysis_timestamp": timezone.now(),
        }
        return results

    def analyze_projects(self):
        """
        Analyze project performance combining DQE, G-Projet, and G-Stock data
        """
        project_analytics = []

        # Get latest data from each source
        latest_dqe = DQEData.objects.order_by("-created_at").first()
        latest_gprojet = GProjet.objects.order_by("-created_at").first()

        if latest_dqe and latest_gprojet:
            # Process DQE data for financial metrics
            dqe_data = latest_dqe.data
            gprojet_data = latest_gprojet.data

            # Extract projects from data
            projects = self._extract_projects_data(dqe_data, gprojet_data)

            for project in projects:
                analytics = self._calculate_project_metrics(project)

                # Save to database
                project_analytics_obj, created = (
                    ProjectAnalytics.objects.update_or_create(
                        project_name=analytics["project_name"], defaults=analytics
                    )
                )

                project_analytics.append(analytics)

        return project_analytics

    def analyze_stock_performance(self):
        """
        Analyze stock management efficiency
        """
        # Get latest stock data
        latest_appro = GStockApprovisionnement.objects.order_by("-created_at").first()
        latest_sortie = GStockSortie.objects.order_by("-created_at").first()
        latest_conso = GStockConsommation.objects.order_by("-created_at").first()
        latest_achat = GStockAchat.objects.order_by("-created_at").first()

        if all([latest_appro, latest_sortie, latest_conso, latest_achat]):
            stock_metrics = self._calculate_stock_metrics(
                latest_appro.data,
                latest_sortie.data,
                latest_conso.data,
                latest_achat.data,
            )

            # Save to database
            period_start = timezone.now().date().replace(day=1)
            period_end = timezone.now().date()

            stock_analytics, created = StockAnalytics.objects.update_or_create(
                analysis_period="monthly",
                period_start=period_start,
                period_end=period_end,
                defaults=stock_metrics,
            )

            return stock_metrics

        return {}

    def calculate_business_kpis(self):
        """
        Calculate high-level business KPIs
        """
        today = timezone.now().date()

        # Aggregate data from recent analytics
        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=today - timedelta(days=30)
        )

        recent_stock = StockAnalytics.objects.filter(
            period_end__gte=today - timedelta(days=30)
        ).first()

        kpis = {
            "project_completion_rate": self._calculate_completion_rate(recent_projects),
            "average_budget_variance": self._calculate_avg_budget_variance(
                recent_projects
            ),
            "stock_efficiency_score": self._calculate_stock_efficiency(recent_stock),
            "overall_performance_score": 0.0,  # Calculated below
        }

        # Calculate overall performance score
        kpis["overall_performance_score"] = (
            kpis["project_completion_rate"] * 0.4
            + (100 - abs(kpis["average_budget_variance"])) * 0.3
            + kpis["stock_efficiency_score"] * 0.3
        )

        # Save to database
        business_kpi, created = BusinessKPIs.objects.update_or_create(
            kpi_date=today,
            kpi_type="daily",
            defaults={
                "project_completion_rate": kpis["project_completion_rate"],
                "average_project_delay": kpis["average_budget_variance"],
                "kpi_data": kpis,
            },
        )

        return kpis

    def generate_alerts(self):
        """
        Generate automated alerts based on thresholds
        """
        alerts_created = []

        # Check for budget overruns
        budget_alerts = self._check_budget_alerts()
        alerts_created.extend(budget_alerts)

        # Check for stock shortages
        stock_alerts = self._check_stock_alerts()
        alerts_created.extend(stock_alerts)

        # Check for schedule delays
        schedule_alerts = self._check_schedule_alerts()
        alerts_created.extend(schedule_alerts)

        return alerts_created

    # Helper methods
    def _extract_projects_data(self, dqe_data, gprojet_data):
        """Extract and combine project data from different sources"""
        projects = []

        # This would depend on the actual structure of your API data
        # You'll need to adapt this based on your real data format

        # Example structure - adapt to your actual data
        if isinstance(dqe_data, dict) and "projects" in dqe_data:
            for project in dqe_data["projects"]:
                project_info = {
                    "name": project.get("name", "Unknown Project"),
                    "estimated_budget": project.get("estimated_cost", 0),
                    "dqe_data": project,
                }

                # Find corresponding G-Projet data
                gprojet_info = self._find_project_in_gprojet(
                    project_info["name"], gprojet_data
                )
                if gprojet_info:
                    project_info["gprojet_data"] = gprojet_info

                projects.append(project_info)

        return projects

    def _calculate_project_metrics(self, project_data):
        """Calculate analytics metrics for a single project"""
        metrics = {
            "project_name": project_data["name"],
            "estimated_budget": Decimal(str(project_data.get("estimated_budget", 0))),
            "data_sources": ["DQE", "G-Projet"],
        }

        # Calculate budget variance if actual cost is available
        if "actual_cost" in project_data:
            actual_cost = Decimal(str(project_data["actual_cost"]))
            metrics["actual_cost"] = actual_cost
            metrics["budget_variance"] = actual_cost - metrics["estimated_budget"]

            if metrics["estimated_budget"] > 0:
                metrics["budget_variance_percentage"] = float(
                    (metrics["budget_variance"] / metrics["estimated_budget"]) * 100
                )

        # Calculate completion percentage from G-Projet data
        if "gprojet_data" in project_data:
            gprojet_info = project_data["gprojet_data"]
            metrics["completion_percentage"] = gprojet_info.get("completion_rate", 0.0)

            # Determine delivery performance
            if metrics["completion_percentage"] >= 100:
                metrics["delivery_performance"] = "Complete"
            elif metrics["completion_percentage"] >= 75:
                metrics["delivery_performance"] = "On-track"
            else:
                metrics["delivery_performance"] = "At-risk"

        return metrics

    def _calculate_stock_metrics(self, appro_data, sortie_data, conso_data, achat_data):
        """Calculate stock performance metrics"""
        metrics = {
            "total_stock_value": Decimal("0"),
            "stock_turnover_ratio": 0.0,
            "stockout_incidents": 0,
            "materials_performance": {},
        }

        # Process stock data based on your actual API structure
        # This is a template - adapt to your real data format

        if isinstance(appro_data, dict) and "total_value" in appro_data:
            metrics["total_stock_value"] = Decimal(str(appro_data["total_value"]))

        if isinstance(sortie_data, dict) and "turnover_rate" in sortie_data:
            metrics["stock_turnover_ratio"] = float(sortie_data["turnover_rate"])

        return metrics

    def _check_budget_alerts(self):
        """Check for budget-related alerts"""
        alerts = []

        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=timezone.now() - timedelta(days=7),
            budget_variance_percentage__isnull=False,
        )

        for project in recent_projects:
            if (
                abs(project.budget_variance_percentage)
                > self.alert_thresholds["budget_variance"]
            ):
                severity = (
                    "high" if abs(project.budget_variance_percentage) > 20 else "medium"
                )

                alert = AlertSystem.objects.create(
                    alert_type="budget_overrun",
                    severity=severity,
                    title=f"Budget variance detected for {project.project_name}",
                    message=f"Project {project.project_name} has a budget variance of {project.budget_variance_percentage:.1f}%",
                    project_name=project.project_name,
                    threshold_value=self.alert_thresholds["budget_variance"],
                    actual_value=abs(project.budget_variance_percentage),
                    variance_percentage=project.budget_variance_percentage,
                )
                alerts.append(alert)

        return alerts

    def _check_stock_alerts(self):
        """Check for stock-related alerts"""
        alerts = []

        # Get latest stock data and check for shortages
        latest_conso = GStockConsommation.objects.order_by("-created_at").first()

        if latest_conso and isinstance(latest_conso.data, dict):
            # Analyze consumption data for potential shortages
            # This depends on your actual data structure
            consumption_data = latest_conso.data

            # Example logic - adapt to your data
            if "stock_levels" in consumption_data:
                for item, level in consumption_data["stock_levels"].items():
                    if level < self.alert_thresholds["stock_shortage"]:
                        alert = AlertSystem.objects.create(
                            alert_type="stock_shortage",
                            severity="medium",
                            title=f"Low stock alert for {item}",
                            message=f"Stock level for {item} is at {level}%, below threshold",
                            affected_area="Stock Management",
                            threshold_value=self.alert_thresholds["stock_shortage"],
                            actual_value=level,
                        )
                        alerts.append(alert)

        return alerts

    def _check_schedule_alerts(self):
        """Check for schedule-related alerts"""
        alerts = []

        # Check projects for schedule delays
        at_risk_projects = ProjectAnalytics.objects.filter(
            delivery_performance="At-risk",
            analysis_date__gte=timezone.now() - timedelta(days=7),
        )

        for project in at_risk_projects:
            alert = AlertSystem.objects.create(
                alert_type="schedule_delay",
                severity="medium",
                title=f"Schedule risk for {project.project_name}",
                message=f"Project {project.project_name} is at risk of schedule delays",
                project_name=project.project_name,
                actual_value=project.completion_percentage or 0,
            )
            alerts.append(alert)

        return alerts
