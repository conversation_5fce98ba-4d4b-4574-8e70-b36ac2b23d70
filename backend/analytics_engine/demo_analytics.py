#!/usr/bin/env python3
"""
Démonstration des classes Analytics avec données réelles
"""

from analytics_engine.analytic_engine import (
    DQEAnalytics,
    GStockApprovisionnementAnalytics,
    EcoleTalentsAnalytics,
    GLocativeAnalytics,
    ESyndicAnalytics,
    SentinelleAnalytics
)

print("DÉMONSTRATION DES ANALYTICS AVEC DONNÉES RÉELLES")
print("=" * 60)

# 1. Démonstration DQE Analytics
print("\n1. DÉMONSTRATION DQE ANALYTICS")
print("=" * 40)
dqe = DQEAnalytics()
if dqe.load_data() and dqe.clean_data():
    dqe.display_dataframes()

# 2. Démonstration G-Stock Approvisionnement
print("\n\n2. DÉMONSTRATION G-STOCK APPROVISIONNEMENT")
print("=" * 50)
gstock = GStockApprovisionnementAnalytics()
if gstock.load_data() and gstock.clean_data():
    gstock.display_dataframes()

# 3. Démonstration École des Talents
print("\n\n3. DÉMONSTRATION ÉCOLE DES TALENTS")
print("=" * 40)
ecole = EcoleTalentsAnalytics()
if ecole.load_data() and ecole.clean_data():
    ecole.display_dataframes()

# 4. Démonstration G-Locative
print("\n\n4. DÉMONSTRATION G-LOCATIVE")
print("=" * 40)
glocative = GLocativeAnalytics()
if glocative.load_data() and glocative.clean_data():
    glocative.display_dataframes()

# 5. Démonstration E-Syndic
print("\n\n5. DÉMONSTRATION E-SYNDIC")
print("=" * 40)
esyndic = ESyndicAnalytics()
if esyndic.load_data() and esyndic.clean_data():
    esyndic.display_dataframes()

# 6. Démonstration Sentinelle
print("\n\n6. DÉMONSTRATION SENTINELLE")
print("=" * 40)
sentinelle = SentinelleAnalytics()
if sentinelle.load_data() and sentinelle.clean_data():
    sentinelle.display_dataframes()

print("\n" + "="*60)
print("FIN DE LA DÉMONSTRATION COMPLÈTE")
print("6 CLASSES AVEC DONNÉES RÉELLES")
print("="*60)
