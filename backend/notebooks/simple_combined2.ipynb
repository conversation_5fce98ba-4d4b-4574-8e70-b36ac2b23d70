{"cells": [{"cell_type": "code", "execution_count": null, "id": "762c1f09", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.express as px\n", "\n", "# Charger les fichiers CSV\n", "def load_data(file_path_glocative, file_path_esyndic):\n", "    df_glocative = pd.read_csv(file_path_glocative)\n", "    df_esyndic = pd.read_csv(file_path_esyndic)\n", "    return df_glocative, df_esyndic\n", "\n", "# Nettoyer les données\n", "def clean_data(df_glocative, df_esyndic):\n", "    # Nettoyage de glocative\n", "    df_glocative_clean = df_glocative.drop(columns=[col for col in df_glocative.columns if 'id' in col.lower()])\n", "    df_glocative_clean = df_glocative_clean.rename(columns={'libelle': 'bien'})\n", "    df_glocative_clean.columns = [col.capitalize() for col in df_glocative_clean.columns]\n", "\n", "    # Nettoyage de esyndic\n", "    df_esyndic_clean = df_esyndic.drop(columns=['id', 'lieu', 'description', 'create_date'])\n", "    df_esyndic_clean = df_esyndic_clean.rename(columns={'libelle': 'programme'})\n", "    df_esyndic_clean.columns = [col.capitalize() for col in df_esyndic_clean.columns]\n", "\n", "    return df_glocative_clean, df_esyndic_clean\n", "\n", "# Fonction pour éclater les colonnes imbriquées\n", "def explode_nested_columns(df, nested_columns):\n", "    for col in nested_columns:\n", "        if col in df.columns:\n", "            df = df.explode(col)\n", "            # Si les données imbriquées sont des chaînes JSON, on peut les éclater davantage\n", "            try:\n", "                df[col] = df[col].apply(eval).apply(pd.Series)\n", "            except:\n", "                pass\n", "    return df\n", "\n", "# Visualisation des problèmes de maintenance les plus courants\n", "def plot_common_maintenance_issues(df_esyndic):\n", "    if 'Incidents' in df_esyndic.columns:\n", "        incidents = df_esyndic['Incidents'].explode()\n", "        incident_counts = incidents.value_counts()\n", "        fig = px.bar(incident_counts, x=incident_counts.index, y=incident_counts.values,\n", "                     labels={'x': 'Type d\\'incident', 'y': 'Nombre d\\'occurrences'},\n", "                     title='Problèmes de maintenance les plus courants')\n", "        fig.show()\n", "\n", "# Visualisation de l'évolution des incidents signalés\n", "def plot_incidents_over_time(df_esyndic):\n", "    if 'Date' in df_esyndic.columns:\n", "        df_esyndic['Date'] = pd.to_datetime(df_esyndic['Date'])\n", "        incidents_over_time = df_esyndic.set_index('Date')['Incidents'].explode().resample('M').count()\n", "        fig = px.line(incidents_over_time, x=incidents_over_time.index, y=incidents_over_time.values,\n", "                      labels={'x': 'Date', 'y': 'Nombre d\\'incidents'},\n", "                      title='Évolution des incidents signalés au fil du temps')\n", "        fig.show()\n", "\n", "# Visualisation des coûts de maintenance par type de bien\n", "def plot_maintenance_costs_by_property_type(df_glocative):\n", "    if 'Charges' in df_glocative.columns and 'Type' in df_glocative.columns:\n", "        df_glocative['Charges'] = df_glocative['Charges'].apply(lambda x: eval(x)['montant'] if isinstance(x, str) else x)\n", "        maintenance_costs = df_glocative.groupby('Type')['Charges'].sum().reset_index()\n", "        fig = px.pie(maintenance_costs, names='Type', values='Charges',\n", "                     title='Coûts de maintenance par type de bien')\n", "        fig.show()\n", "\n", "# Charger les données\n", "df_glocative, df_esyndic = load_data('G_locative.csv', 'E_syndic.csv')\n", "\n", "# Nettoyer les données\n", "df_glocative_clean, df_esyndic_clean = clean_data(df_glocative, df_esyndic)\n", "\n", "# Éclater les colonnes imbriquées\n", "nested_columns_glocative = ['proprietaire', 'locataires', 'charges', 'types', 'actifs']\n", "nested_columns_esyndic = ['proprietaire', 'locataires', 'charges', 'incidents', 'evenements']\n", "\n", "df_glocative_exploded = explode_nested_columns(df_glocative_clean, nested_columns_glocative)\n", "df_esyndic_exploded = explode_nested_columns(df_esyndic_clean, nested_columns_esyndic)\n", "\n", "# Afficher les DataFrames nettoyés et éclatés\n", "print(\"DataFrame Glocative nettoyé et éclaté :\")\n", "print(df_glocative_exploded.head())\n", "\n", "print(\"\\nDataFrame Esyndic nettoyé et éclaté :\")\n", "print(df_esyndic_exploded.head())\n", "\n", "# Visualiser les problèmes de maintenance les plus courants\n", "plot_common_maintenance_issues(df_esyndic_exploded)\n", "\n", "# Visualiser l'évolution des incidents signalés\n", "plot_incidents_over_time(df_esyndic_exploded)\n", "\n", "# Visualiser les coûts de maintenance par type de bien\n", "plot_maintenance_costs_by_property_type(df_glocative_exploded)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}