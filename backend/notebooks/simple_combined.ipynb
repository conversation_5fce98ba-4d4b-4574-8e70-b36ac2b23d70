import pandas as pd
import json
import plotly.express as px
import warnings

def charger_donnees(e_syndic.csv, g_locative.csv):
    """
    Charge les données depuis les fichiers CSV spécifiés.

    Args:
        fichier_esyndic (str): Chemin vers le fichier E-Syndic.
        fichier_glocative (str): Chemin vers le fichier G-Locative.

    Returns:
        tuple: Un tuple contenant les DataFrames esyndic et glocative.
    """
    try:
        df_esyndic_raw = pd.read_csv(fichier_esyndic)
        df_glocative_raw = pd.read_csv(fichier_glocative)
        print("Fichiers CSV chargés avec succès.")
        return df_esyndic_raw, df_glocative_raw
    except FileNotFoundError as e:
        print(f"Erreur de chargement de fichier : {e}")
        return None, None

def preparer_donnees_parents(df_esyndic_raw, df_glocative_raw):
    """
    Nettoie et prépare les DataFrames principaux.

    Args:
        df_esyndic_raw (pd.DataFrame): DataFrame brut de E-Syndic.
        df_glocative_raw (pd.DataFrame): DataFrame brut de G-Locative.

    Returns:
        tuple: DataFrames nettoyés esyndic et glocative.
    """
    # --- Traitement E-Syndic ---
    # Parser la colonne 'data' qui contient une chaîne JSON
    try:
        data_esyndic = json.loads(df_esyndic_raw['data'].iloc[0])
        df_esyndic = pd.json_normalize(data_esyndic['data'])
    except (json.JSONDecodeError, KeyError) as e:
        print(f"Erreur lors du parsing des données E-Syndic : {e}")
        return None, None

    # Suppression et renommage des colonnes pour E-Syndic
    df_esyndic = df_esyndic.drop(columns=['id', 'lieu', 'description', 'created_at'], errors='ignore')
    df_esyndic = df_esyndic.rename(columns={'libelle': 'Programme'})

    # --- Traitement G-Locative ---
    df_glocative = df_glocative_raw.copy()
    # Suppression des colonnes liées aux IDs
    cols_to_drop_glocative = [col for col in df_glocative.columns if 'id' in col.lower()]
    df_glocative = df_glocative.drop(columns=cols_to_drop_glocative, errors='ignore')
    df_glocative = df_glocative.rename(columns={'libelle': 'Bien'})

    # --- Capitalisation des noms de colonnes pour les deux DataFrames ---
    df_esyndic.columns = [col.capitalize() for col in df_esyndic.columns]
    df_glocative.columns = [col.capitalize() for col in df_glocative.columns]

    print("\n--- DataFrame E-Syndic Nettoyé ---")
    display(df_esyndic.head())
    print("\n--- DataFrame G-Locative Nettoyé ---")
    display(df_glocative.head())

    return df_esyndic, df_glocative

def eclater_donnees_imbriquees(df_esyndic, df_glocative):
    """
    Extrait et normalise les données imbriquées des DataFrames.

    Args:
        df_esyndic (pd.DataFrame): DataFrame E-Syndic nettoyé.
        df_glocative (pd.DataFrame): DataFrame G-Locative nettoyé.

    Returns:
        dict: Un dictionnaire contenant tous les DataFrames éclatés.
    """
    dataframes_eclates = {}

    def safe_json_loads(series):
        results = []
        for item in series:
            try:
                # Gérer les chaînes vides ou invalides
                if isinstance(item, str) and item.strip():
                    results.append(json.loads(item))
                # Gérer les listes ou dicts déjà parsés
                elif isinstance(item, (list, dict)):
                    results.append(item)
                else:
                    results.append([]) # ou None, ou {}
            except (json.JSONDecodeError, TypeError):
                results.append([]) # ou None, ou {}
        return results

    # --- Éclatement pour G-Locative ---
    print("\n--- Éclatement des données G-Locative ---")
    cols_glocative = ['Proprietaires', 'Locataires', 'Charges', 'Type', 'Actifs']
    for col in cols_glocative:
        if col in df_glocative.columns:
            # Associer les données éclatées avec le 'Bien' parent
            temp_df = df_glocative[['Bien', col]].copy()
            temp_df[col] = safe_json_loads(temp_df[col])
            
            # Éclater la liste en plusieurs lignes
            df_eclate = temp_df.explode(col)
            
            # Normaliser le dict en plusieurs colonnes
            df_normalise = pd.json_normalize(df_eclate[col]).add_prefix(f'{col[:-1].lower()}_')
            
            # Joindre avec le nom du bien
            df_final = pd.concat([df_eclate['Bien'].reset_index(drop=True), df_normalise], axis=1).dropna(subset=[col for col in df_normalise.columns if col.endswith('_id')])
            
            dataframes_eclates[f'glocative_{col.lower()}'] = df_final
            print(f"\nDataFrame '{col}' (Source: G-Locative)")
            display(df_final.head())


    # --- Éclatement pour E-Syndic ---
    print("\n--- Éclatement des données E-Syndic ---")
    cols_esyndic = ['Proprietaire', 'Locataires', 'Incidents', 'Evenements', 'Charges']
    for col in cols_esyndic:
        if col in df_esyndic.columns:
            # Associer les données avec le 'Programme' parent
            temp_df = df_esyndic[['Programme', col]].copy()
            
            # Éclater la liste
            df_eclate = temp_df.explode(col).dropna()
            
            # Normaliser
            df_normalise = pd.json_normalize(df_eclate[col]).add_prefix(f'{col[:-1].lower()}_')
            
            # Joindre
            df_final = pd.concat([df_eclate['Programme'].reset_index(drop=True), df_normalise], axis=1)

            # Cas spécial pour les charges qui sont doublement imbriquées
            if col == 'Charges':
                appel_fonds = pd.json_normalize(df_final['charge_appel_fonds']).add_prefix('appel_fonds_')
                imprevus = pd.json_normalize(df_final['charge_imprevus']).add_prefix('imprevus_')
                df_charges_detail = pd.concat([df_final['Programme'], appel_fonds, imprevus], axis=1)
                
                dataframes_eclates['esyndic_charges_detail'] = df_charges_detail
                print(f"\nDataFrame 'Charges Détaillées' (Source: E-Syndic)")
                display(df_charges_detail.head())
            else:
                 dataframes_eclates[f'esyndic_{col.lower()}'] = df_final
                 print(f"\nDataFrame '{col}' (Source: E-Syndic)")
                 display(df_final.head())


    return dataframes_eclates

def repondre_problemes_maintenance_courants(df_incidents):
    """
    Analyse et visualise les types d'incidents les plus fréquents.

    Args:
        df_incidents (pd.DataFrame): DataFrame des incidents de E-Syndic.
    """
    print("\n" + "="*80)
    print("Question : Quels sont les problèmes de maintenance les plus courants ?")
    print("="*80)

    if df_incidents is None or df_incidents.empty:
        print("Aucune donnée d'incident disponible pour l'analyse.")
        return

    # Compter les occurrences de chaque type d'incident (basé sur le titre)
    counts = df_incidents['incident_titre'].value_counts().reset_index()
    counts.columns = ['Type de problème', 'Nombre d\'incidents']

    # Créer le graphique
    fig = px.bar(
        counts,
        x='Type de problème',
        y='Nombre d\'incidents',
        title='Problèmes de maintenance les plus courants',
        text_auto=True,
        labels={'Type de problème': 'Type de problème de maintenance', 'Nombre d\'incidents': 'Nombre total de signalements'}
    )
    fig.update_layout(xaxis_tickangle=-45)
    fig.show()
    print("\nAnalyse : Le graphique à barres ci-dessus montre la distribution des incidents signalés via E-Syndic. Les catégories avec les barres les plus hautes représentent les problèmes les plus fréquents rencontrés par les résidents.")

# ------------------------------------------------------------------------------

def repondre_evolution_incidents_temporelle(df_incidents):
    """
    Analyse et visualise l'évolution du nombre d'incidents au fil du temps.

    Args:
        df_incidents (pd.DataFrame): DataFrame des incidents de E-Syndic.
    """
    print("\n" + "="*80)
    print("Question : Comment évoluent les incidents signalés via E-Syndic au fil du temps ?")
    print("="*80)
    
    if df_incidents is None or df_incidents.empty:
        print("Aucune donnée d'incident disponible pour l'analyse.")
        return
        
    df = df_incidents.copy()
    # Convertir la colonne de date en format datetime
    # Tente de parser plusieurs formats possibles, gérant les erreurs
    df['date'] = pd.to_datetime(df['incident_date_debut'], errors='coerce', dayfirst=True)
    df.dropna(subset=['date'], inplace=True)
    
    # Agréger par mois
    df['mois'] = df['date'].dt.to_period('M').astype(str)
    incidents_par_mois = df.groupby('mois').size().reset_index(name='nombre_incidents')

    # Créer le graphique
    fig = px.line(
        incidents_par_mois,
        x='mois',
        y='nombre_incidents',
        title='Évolution du nombre d\'incidents signalés par mois',
        markers=True,
        labels={'mois': 'Mois', 'nombre_incidents': 'Nombre d\'incidents'}
    )
    fig.show()
    print("\nAnalyse : Le graphique linéaire illustre les tendances, pics et baisses du nombre d'incidents mensuels. Une tendance à la hausse peut indiquer des problèmes croissants, tandis qu'une baisse peut suggérer une amélioration de la maintenance.")

# ------------------------------------------------------------------------------

def repondre_couts_maintenance_par_type_bien(df_glocative, df_esyndic_charges):
    """
    Analyse et visualise les coûts moyens par type de bien.
    NOTE: Utilise les charges globales comme proxy pour les coûts de maintenance.

    Args:
        df_glocative (pd.DataFrame): DataFrame principal de G-Locative.
        df_esyndic_charges (pd.DataFrame): DataFrame des charges de E-Syndic.
    """
    print("\n" + "="*80)
    print("Question : Comment les coûts de maintenance évoluent-ils en fonction du type de bien ?")
    print("="*80)

    # Analyse basée sur G-Locative (charges globales par bien)
    if df_glocative is not None and not df_glocative.empty:
        df_g = df_glocative.copy()
        
        # S'assurer que les colonnes sont numériques
        df_g['Totalcharges'] = pd.to_numeric(df_g['Totalcharges'], errors='coerce')
        
        # Extraire le libellé du type de bien
        df_g['type_libelle'] = df_g['Type'].apply(lambda x: json.loads(x).get('libelle') if isinstance(x, str) else (x.get('libelle') if isinstance(x, dict) else 'Inconnu'))
        
        # Calculer la moyenne des charges par type
        couts_par_type = df_g.groupby('type_libelle')['Totalcharges'].mean().reset_index()
        
        fig_gloc = px.bar(
            couts_par_type,
            x='type_libelle',
            y='Totalcharges',
            title='Moyenne des charges totales par type de bien (Source: G-Locative)',
            text_auto='.2s',
            labels={'type_libelle': 'Type de Bien', 'Totalcharges': 'Montant moyen des charges (CFA)'}
        )
        fig_gloc.show()
        print("\nAnalyse (G-Locative) : Ce graphique montre la moyenne des charges totales (incluant loyer, maintenance, etc.) pour chaque type de bien. Il donne une vue d'ensemble des coûts associés à chaque catégorie de propriété.")

    # Analyse basée sur E-Syndic (charges exceptionnelles par programme)
    if df_esyndic_charges is not None and not df_esyndic_charges.empty:
        df_e = df_esyndic_charges.copy()
        
        # Les imprévus sont un bon indicateur des coûts de maintenance non planifiés
        couts_imprevus = df_e[['Programme', 'imprevus_total_charge_exceptionnel']].copy()
        couts_imprevus.rename(columns={'imprevus_total_charge_exceptionnel': 'CoutsImprevus'}, inplace=True)
        
        fig_esyn = px.bar(
            couts_imprevus,
            x='Programme',
            y='CoutsImprevus',
            title='Coûts des imprévus par programme (Source: E-Syndic)',
            text_auto='.2s',
            labels={'Programme': 'Nom du Programme Immobilier', 'CoutsImprevus': 'Total des charges exceptionnelles (CFA)'}
        )
        fig_esyn.show()
        print("\nAnalyse (E-Syndic) : Ce graphique isole les 'charges exceptionnelles' ou imprévus, qui correspondent souvent à des coûts de maintenance corrective. Il permet d'identifier les programmes immobiliers qui génèrent le plus de dépenses imprévues.")

def main():
    """
    Fonction principale pour exécuter l'ensemble du processus d'analyse.
    """
    # Étape 1: Charger les données
    df_esyndic_raw, df_glocative_raw = charger_donnees(
        fichier_esyndic='E_syndic.csv',
        fichier_glocative='G_locative.csv'
    )
    if df_esyndic_raw is None:
        return # Arrêter si le chargement a échoué

    # Étape 2: Nettoyer les DataFrames parents
    df_esyndic, df_glocative = preparer_donnees_parents(df_esyndic_raw, df_glocative_raw)
    if df_esyndic is None:
        return

    # Étape 3: Éclater les données imbriquées
    donnees = eclater_donnees_imbriquees(df_esyndic, df_glocative)

    # Étape 4: Exécuter les fonctions d'analyse pour répondre aux questions
    # Note: On récupère les dataframes éclatés depuis le dictionnaire 'donnees'
    
    # Q1: Problèmes de maintenance courants
    repondre_problemes_maintenance_courants(donnees.get('esyndic_incidents'))

    # Q2: Évolution des incidents dans le temps
    repondre_evolution_incidents_temporelle(donnees.get('esyndic_incidents'))

    # Q3: Coûts par type de bien
    repondre_couts_maintenance_par_type_bien(df_glocative, donnees.get('esyndic_charges_detail'))


if __name__ == '__main__':
    main()
