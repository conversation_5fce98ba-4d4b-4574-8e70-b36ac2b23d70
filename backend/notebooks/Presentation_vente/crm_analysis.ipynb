{"cells": [{"cell_type": "markdown", "id": "4f2acd64", "metadata": {}, "source": ["# Analyse des données CRM\n", "## Chargement et analyse des données de présentation/vente"]}, {"cell_type": "markdown", "id": "407f8274", "metadata": {}, "source": ["### 1. Imports des bibliothèques"]}, {"cell_type": "code", "execution_count": 19, "id": "5287c614", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import ast\n", "import json"]}, {"cell_type": "markdown", "id": "5a8446da", "metadata": {}, "source": ["### 2. Chargement du fichier CSV"]}, {"cell_type": "code", "execution_count": 20, "id": "b559c854", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV chargé - Shape: (1, 2)\n", "Colonnes: ['status', 'data']\n", "Status: success\n"]}], "source": ["# Chargement du fichier CSV\n", "df_csv = pd.read_csv('crm_data.csv')\n", "print(f\"CSV chargé - Shape: {df_csv.shape}\")\n", "print(f\"Colonnes: {list(df_csv.columns)}\")\n", "print(f\"Status: {df_csv['status'].iloc[0]}\")"]}, {"cell_type": "markdown", "id": "f78eb129", "metadata": {}, "source": ["### 3. Parsing des données JSON depuis le CSV"]}, {"cell_type": "code", "execution_count": 21, "id": "e828e04c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nombre de programmes dans les données: 4\n", "\n", "Premier programme (aperçu):\n", "Code: KAY0075\n", "Libellé: BOREFLETS\n", "Localisation: Cocody angré\n"]}], "source": ["# Extraction des données JSON depuis la colonne 'data'\n", "json_data_str = df_csv['data'].iloc[0]\n", "json_data = ast.literal_eval(json_data_str)\n", "\n", "print(f\"Nombre de programmes dans les données: {len(json_data)}\")\n", "print(\"\\nPremier programme (aperçu):\")\n", "print(f\"Code: {json_data[0]['code']}\")\n", "print(f\"Libellé: {json_data[0]['libelle']}\")\n", "print(f\"Localisation: {json_data[0]['localisation']}\")"]}, {"cell_type": "markdown", "id": "359a7828", "metadata": {}, "source": ["### 4. <PERSON><PERSON><PERSON> du DataFrame parent"]}, {"cell_type": "code", "execution_count": 22, "id": "dfdb407e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame parent créé avec 4 programmes\n", "Colonnes: ['id', 'code', 'slug', 'libelle', 'localisation', 'img', 'nb_logs', 'ventes', 'chiffre_affaire', 'recouvrement', 'reste_a_recouvrer', 'montant_vh']\n"]}], "source": ["# Création du DataFrame parent avec toutes les informations principales\n", "programmes_data = []\n", "\n", "for programme in json_data:\n", "    programme_info = {\n", "        'id': programme.get('id'),\n", "        'code': programme.get('code'),\n", "        'slug': programme.get('slug'),\n", "        'libelle': programme.get('libelle'),\n", "        'localisation': programme.get('localisation'),\n", "        'img': programme.get('img'),\n", "        'nb_logs': len(programme.get('log', []))\n", "    }\n", "    \n", "    # Ajouter les statistiques si elles existent\n", "    if 'statistiques' in programme:\n", "        stats = programme['statistiques']\n", "        programme_info.update({\n", "            'ventes': stats.get('ventes', 0),\n", "            'chiffre_affaire': stats.get('chiffre_affaire', 0),\n", "            'recouvrement': stats.get('recouvrement', 0),\n", "            'reste_a_recouvrer': stats.get('reste_a_recouvrer', 0),\n", "            'montant_vh': stats.get('montant_vh', 0)\n", "        })\n", "    \n", "    programmes_data.append(programme_info)\n", "\n", "# Création du DataFrame parent\n", "df_parent = pd.DataFrame(programmes_data)\n", "print(f\"DataFrame parent créé avec {len(df_parent)} programmes\")\n", "print(f\"Colonnes: {list(df_parent.columns)}\")"]}, {"cell_type": "markdown", "id": "fb33a0e2", "metadata": {}, "source": ["### 5. Data Cleaning - Suppression et renommage des colonnes"]}, {"cell_type": "code", "execution_count": 23, "id": "fe8f91e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATA CLEANING ===\n", "Colonnes avant nettoyage: ['id', 'code', 'slug', 'libelle', 'localisation', 'img', 'nb_logs', 'ventes', 'chiffre_affaire', 'recouvrement', 'reste_a_recouvrer', 'montant_vh']\n", "<PERSON>onne 'id' supprimée\n", "<PERSON><PERSON><PERSON> 'slug' supprimée\n", "Colonne 'libelle' renommée en 'programme' et mise en majuscules\n", "Colonne 'localisation' mise en majuscules\n", "\n", "Colonnes après nettoyage: ['code', 'programme', 'localisation', 'img', 'nb_logs', 'ventes', 'chiffre_affaire', 'recouvrement', 'reste_a_recouvrer', 'montant_vh']\n"]}], "source": ["print(\"=== DATA CLEANING ===\")\n", "print(f\"Colonnes avant nettoyage: {list(df_parent.columns)}\")\n", "\n", "# Suppression des colonnes id et slug\n", "columns_to_drop = ['id', 'slug']\n", "for col in columns_to_drop:\n", "    if col in df_parent.columns:\n", "        df_parent = df_parent.drop(columns=[col])\n", "        print(f\"Colonne '{col}' supprimée\")\n", "\n", "# Renommage de libelle en programme et mise en majuscules\n", "if 'libelle' in df_parent.columns:\n", "    df_parent = df_parent.rename(columns={'libelle': 'programme'})\n", "    df_parent['programme'] = df_parent['programme'].str.upper()\n", "    print(\"Colonne 'libelle' renommée en 'programme' et mise en majuscules\")\n", "\n", "# Mise en majuscules de la localisation\n", "if 'localisation' in df_parent.columns:\n", "    df_parent['localisation'] = df_parent['localisation'].str.upper()\n", "    print(\"Colonne 'localisation' mise en majuscules\")\n", "\n", "print(f\"\\nColonnes après nettoyage: {list(df_parent.columns)}\")"]}, {"cell_type": "markdown", "id": "bddb5b46", "metadata": {}, "source": ["### 6. Fonctions d'éclatement des données"]}, {"cell_type": "code", "execution_count": 24, "id": "0c7ee78f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fonctions d'éclatement définies\n"]}], "source": ["def extract_logs_data():\n", "    \"\"\"Extraction des données de logs pour chaque programme\"\"\"\n", "    logs_data = []\n", "    \n", "    for programme in json_data:\n", "        programme_name = programme['libelle'].upper()\n", "        \n", "        if 'log' in programme:\n", "            for log in programme['log']:\n", "                log_info = {\n", "                    'programme': programme_name,\n", "                    'type_id': log.get('typeclient_id'),\n", "                    'action': log.get('action'),\n", "                    'commentaire': log.get('commentaire')\n", "                }\n", "                logs_data.append(log_info)\n", "    \n", "    return pd.DataFrame(logs_data)\n", "\n", "def extract_users_data():\n", "    \"\"\"Extraction des données utilisateurs pour chaque programme\"\"\"\n", "    users_data = []\n", "    \n", "    for programme in json_data:\n", "        programme_name = programme['libelle'].upper()\n", "        \n", "        if 'log' in programme:\n", "            for log in programme['log']:\n", "                if 'user' in log:\n", "                    user = log['user']\n", "                    user_info = {\n", "                        'programme': programme_name,\n", "                        'firstname': user.get('firstname'),\n", "                        'lastname': user.get('lastname'),\n", "                        'fonction': user.get('fonction'),\n", "                        'phone': user.get('phone'),\n", "                        'email': user.get('email')\n", "                    }\n", "                    users_data.append(user_info)\n", "    \n", "    # Supprimer les doublons\n", "    df_users = pd.DataFrame(users_data)\n", "    if not df_users.empty:\n", "        df_users = df_users.drop_duplicates()\n", "    \n", "    return df_users\n", "\n", "def extract_stats_data():\n", "    \"\"\"Extraction des statistiques pour chaque programme\"\"\"\n", "    stats_data = []\n", "    \n", "    for programme in json_data:\n", "        programme_name = programme['libelle'].upper()\n", "        \n", "        if 'statistiques' in programme:\n", "            stats = programme['statistiques']\n", "            stats_info = {\n", "                'programme': programme_name,\n", "                'ventes': stats.get('ventes', 0),\n", "                'chiffre_affaire': stats.get('chiffre_affaire', 0),\n", "                'recouvrement': stats.get('recouvrement', 0),\n", "                'reste_a_recouvrer': stats.get('reste_a_recouvrer', 0),\n", "                'montant_vh': stats.get('montant_vh', 0)\n", "            }\n", "            stats_data.append(stats_info)\n", "    \n", "    return pd.DataFrame(stats_data)\n", "\n", "print(\"Fonctions d'éclatement définies\")"]}, {"cell_type": "markdown", "id": "f622ac94", "metadata": {}, "source": ["### 7. Création des DataFrames éclatés"]}, {"cell_type": "code", "execution_count": 25, "id": "94b295da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame programmes: 4 lignes\n", "DataFrame logs: 9890 lignes\n", "DataFrame users: 42 lignes\n", "DataFrame stats: 4 lignes\n"]}], "source": ["# Création des DataFrames éclatés\n", "df_programmes = df_parent[['code', 'programme', 'localisation']].copy()\n", "df_logs = extract_logs_data()\n", "df_users = extract_users_data()\n", "df_stats = extract_stats_data()\n", "\n", "print(f\"DataFrame programmes: {len(df_programmes)} lignes\")\n", "print(f\"DataFrame logs: {len(df_logs)} lignes\")\n", "print(f\"DataFrame users: {len(df_users)} lignes\")\n", "print(f\"DataFrame stats: {len(df_stats)} lignes\")"]}, {"cell_type": "markdown", "id": "f5b88555", "metadata": {}, "source": ["### 8. Affichage du DataFrame Parent"]}, {"cell_type": "code", "execution_count": 26, "id": "db871d2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATAFRAME PARENT ===\n", "Dimensions: (4, 10)\n", "\n", "Aperçu des données:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>programme</th>\n", "      <th>localisation</th>\n", "      <th>img</th>\n", "      <th>nb_logs</th>\n", "      <th>ventes</th>\n", "      <th>chiffre_affaire</th>\n", "      <th>recouvrement</th>\n", "      <th>reste_a_recouvrer</th>\n", "      <th>montant_vh</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAY0075</td>\n", "      <td>BOREFLETS</td>\n", "      <td>COCODY ANGRÉ</td>\n", "      <td>https://crm.inoovim.com/assets/uploads/program...</td>\n", "      <td>5003</td>\n", "      <td>226</td>\n", "      <td>34374200000</td>\n", "      <td>14757898426</td>\n", "      <td>19616301574</td>\n", "      <td>16654469500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KAY001</td>\n", "      <td>CALLISTO</td>\n", "      <td>BASSAM</td>\n", "      <td>https://crm.inoovim.com/assets/uploads/program...</td>\n", "      <td>84</td>\n", "      <td>1</td>\n", "      <td>92000000</td>\n", "      <td>49850000</td>\n", "      <td>42150000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KAY0076</td>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>QUARTIER PORO STADE 1, COMMUNE DE SAN-PEDRO</td>\n", "      <td>https://crm.inoovim.com/assets/uploads/program...</td>\n", "      <td>1957</td>\n", "      <td>82</td>\n", "      <td>2590280000</td>\n", "      <td>739690528</td>\n", "      <td>1850589472</td>\n", "      <td>1081478000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KAY0077</td>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>ABIDJAN, AHOUÉ</td>\n", "      <td>https://crm.inoovim.com/assets/uploads/program...</td>\n", "      <td>2846</td>\n", "      <td>193</td>\n", "      <td>6603150000</td>\n", "      <td>1855963994</td>\n", "      <td>4747186006</td>\n", "      <td>4261458000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      code                       programme  \\\n", "0  KAY0075                       BOREFLETS   \n", "1   KAY001                        CALLISTO   \n", "2  KAY0076           LES RESIDENCES KOTIBE   \n", "3  KAY0077  RESIDENCES LES JARDINS D'AHOUE   \n", "\n", "                                  localisation  \\\n", "0                                 COCODY ANGRÉ   \n", "1                                       BASSAM   \n", "2  QUARTIER PORO STADE 1, COMMUNE DE SAN-PEDRO   \n", "3                               ABIDJAN, AHOUÉ   \n", "\n", "                                                 img  nb_logs  ventes  \\\n", "0  https://crm.inoovim.com/assets/uploads/program...     5003     226   \n", "1  https://crm.inoovim.com/assets/uploads/program...       84       1   \n", "2  https://crm.inoovim.com/assets/uploads/program...     1957      82   \n", "3  https://crm.inoovim.com/assets/uploads/program...     2846     193   \n", "\n", "   chiffre_affaire  recouvrement  reste_a_recouvrer   montant_vh  \n", "0      34374200000   14757898426        19616301574  16654469500  \n", "1         92000000      49850000           42150000            0  \n", "2       2590280000     739690528         1850589472   1081478000  \n", "3       6603150000    1855963994         4747186006   4261458000  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"=== DATAFRAME PARENT ===\")\n", "print(f\"Dimensions: {df_parent.shape}\")\n", "print(\"\\nAperçu des données:\")\n", "display(df_parent)"]}, {"cell_type": "markdown", "id": "bc65f301", "metadata": {}, "source": ["### 9. Affichage du DataFrame des Programmes"]}, {"cell_type": "code", "execution_count": 27, "id": "ef4c8ec6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATAFRAME DES PROGRAMMES ===\n", "Dimensions: (4, 3)\n", "\n", "Liste des programmes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>programme</th>\n", "      <th>localisation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAY0075</td>\n", "      <td>BOREFLETS</td>\n", "      <td>COCODY ANGRÉ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KAY001</td>\n", "      <td>CALLISTO</td>\n", "      <td>BASSAM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KAY0076</td>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>QUARTIER PORO STADE 1, COMMUNE DE SAN-PEDRO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KAY0077</td>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>ABIDJAN, AHOUÉ</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      code                       programme  \\\n", "0  KAY0075                       BOREFLETS   \n", "1   KAY001                        CALLISTO   \n", "2  KAY0076           LES RESIDENCES KOTIBE   \n", "3  KAY0077  RESIDENCES LES JARDINS D'AHOUE   \n", "\n", "                                  localisation  \n", "0                                 COCODY ANGRÉ  \n", "1                                       BASSAM  \n", "2  QUARTIER PORO STADE 1, COMMUNE DE SAN-PEDRO  \n", "3                               ABIDJAN, AHOUÉ  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"=== DATAFRAME DES PROGRAMMES ===\")\n", "print(f\"Dimensions: {df_programmes.shape}\")\n", "print(\"\\nListe des programmes:\")\n", "display(df_programmes)"]}, {"cell_type": "markdown", "id": "ac7499c3", "metadata": {}, "source": ["### 10. Affichage du DataFrame des Logs"]}, {"cell_type": "code", "execution_count": 28, "id": "a413fd7c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATAFRAME DES LOGS ===\n", "Dimensions: (9890, 4)\n", "\n", "Actions les plus fréquentes:\n", "action\n", "Enregistré un règlement client           1581\n", "Mise à jour du KYC                       1479\n", "Transmission d'une offre                  909\n", "Profil client accepté                     687\n", "Création d'une opportunité de vente       600\n", "Signature de l'offre de vente             595\n", "Migré l'opportunité de vente              594\n", "Enregistrement des échéanciers signés     540\n", "Enregistrement d'une souscription         531\n", "Transmission d'accord signé               530\n", "Name: count, dtype: int64\n", "\n", "Logs par programme:\n", "programme\n", "BOREFLETS                         5003\n", "RESIDENCES LES JARDINS D'AHOUE    2846\n", "LES RESIDENCES KOTIBE             1957\n", "CALLISTO                            84\n", "Name: count, dtype: int64\n", "\n", "Aperçu des logs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>programme</th>\n", "      <th>type_id</th>\n", "      <th>action</th>\n", "      <th>commentaire</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BOREFLETS</td>\n", "      <td>1</td>\n", "      <td>Ajout d'un suspect</td>\n", "      <td>Le suspect &lt;b class=\"text-warning\"&gt;Kprakpra Ge...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>BOREFLETS</td>\n", "      <td>2</td>\n", "      <td>Contact établi</td>\n", "      <td>Le contact avec le suspect &lt;b class=\"text-warn...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BOREFLETS</td>\n", "      <td>2</td>\n", "      <td>Mise à jour du KYC</td>\n", "      <td>Mise à jour du KYC du client en Prospect .&lt;b c...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Enregistrement d'une intention d'achat</td>\n", "      <td>L'intention d'achat du client  &lt;b class=\"text-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BOREFLETS</td>\n", "      <td>1</td>\n", "      <td>Ajout d'un suspect</td>\n", "      <td>Le suspect &lt;b class=\"text-warning\"&gt;EGUE OMO DE...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Transmission d'une offre</td>\n", "      <td>Transmis les offres de vente et conditions au ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>BOREFLETS</td>\n", "      <td>2</td>\n", "      <td>Contact établi</td>\n", "      <td>Le contact avec le suspect &lt;b class=\"text-warn...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Mise à jour du KYC</td>\n", "      <td>Mise à jour du KYC du client en Analyse .&lt;b cl...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Mise à jour du KYC</td>\n", "      <td>Mise à jour du KYC du client en Analyse .&lt;b cl...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>BOREFLETS</td>\n", "      <td>2</td>\n", "      <td>Mise à jour du KYC</td>\n", "      <td>Mise à jour du KYC du client en Prospect .&lt;b c...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Enregistrement d'une intention d'achat</td>\n", "      <td>L'intention d'achat du client  &lt;b class=\"text-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Transmission d'une offre</td>\n", "      <td>Transmis les offres de vente et conditions au ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Profil client accepté</td>\n", "      <td>Le profil du client  &lt;b class=\"text-warning\"&gt;G...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>BOREFLETS</td>\n", "      <td>4</td>\n", "      <td>Transmission d'accord signé</td>\n", "      <td>Transmis l'accord signé par INOOVIM et le clie...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Mise à jour du KYC</td>\n", "      <td>Mise à jour du KYC du client en Analyse .&lt;b cl...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Transmission d'une offre</td>\n", "      <td>Transmis les offres de vente et conditions au ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>BOREFLETS</td>\n", "      <td>3</td>\n", "      <td>Profil client accepté</td>\n", "      <td>Le profil du client  &lt;b class=\"text-warning\"&gt;O...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>BOREFLETS</td>\n", "      <td>4</td>\n", "      <td>Transmission d'accord signé</td>\n", "      <td>Transmis l'accord signé par INOOVIM et le clie...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>BOREFLETS</td>\n", "      <td>4</td>\n", "      <td>Création d'une opportunité de vente</td>\n", "      <td>Une Opportunité de vente a été créée pour le c...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>BOREFLETS</td>\n", "      <td>4</td>\n", "      <td>Signature de l'offre de vente</td>\n", "      <td>Le client  &lt;b class=\"text-warning\"&gt;<PERSON>...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    programme  type_id                                  action  \\\n", "0   BOREFLETS        1                      Ajout d'un suspect   \n", "1   BOREFLETS        2                          Contact établi   \n", "2   BOREFLETS        2                      Mise à jour du KYC   \n", "3   BOREFLETS        3  Enregistrement d'une intention d'achat   \n", "4   BOREFLETS        1                      Ajout d'un suspect   \n", "5   BOREFLETS        3                Transmission d'une offre   \n", "6   BOREFLETS        2                          Contact établi   \n", "7   BOREFLETS        3                      Mise à jour du KYC   \n", "8   BOREFLETS        3                      Mise à jour du KYC   \n", "9   BOREFLETS        2                      Mise à jour du KYC   \n", "10  BOREFLETS        3  Enregistrement d'une intention d'achat   \n", "11  BOREFLETS        3                Transmission d'une offre   \n", "12  BOREFLETS        3                   Profil client accepté   \n", "13  BOREFLETS        4             Transmission d'accord signé   \n", "14  BOREFLETS        3                      Mise à jour du KYC   \n", "15  BOREFLETS        3                Transmission d'une offre   \n", "16  BOREFLETS        3                   Profil client accepté   \n", "17  BOREFLETS        4             Transmission d'accord signé   \n", "18  BOREFLETS        4     Création d'une opportunité de vente   \n", "19  BOREFLETS        4           Signature de l'offre de vente   \n", "\n", "                                          commentaire  \n", "0   Le suspect <b class=\"text-warning\">Kprakpra Ge...  \n", "1   Le contact avec le suspect <b class=\"text-warn...  \n", "2   Mise à jour du KYC du client en Prospect .<b c...  \n", "3   L'intention d'achat du client  <b class=\"text-...  \n", "4   Le suspect <b class=\"text-warning\">EGUE OMO DE...  \n", "5   Transmis les offres de vente et conditions au ...  \n", "6   Le contact avec le suspect <b class=\"text-warn...  \n", "7   Mise à jour du KYC du client en Analyse .<b cl...  \n", "8   Mise à jour du KYC du client en Analyse .<b cl...  \n", "9   Mise à jour du KYC du client en Prospect .<b c...  \n", "10  L'intention d'achat du client  <b class=\"text-...  \n", "11  Transmis les offres de vente et conditions au ...  \n", "12  Le profil du client  <b class=\"text-warning\">G...  \n", "13  Transmis l'accord signé par INOOVIM et le clie...  \n", "14  Mise à jour du KYC du client en Analyse .<b cl...  \n", "15  Transmis les offres de vente et conditions au ...  \n", "16  Le profil du client  <b class=\"text-warning\">O...  \n", "17  Transmis l'accord signé par INOOVIM et le clie...  \n", "18  Une Opportunité de vente a été créée pour le c...  \n", "19  Le client  <b class=\"text-warning\"><PERSON>...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"=== DATAFRAME DES LOGS ===\")\n", "print(f\"Dimensions: {df_logs.shape}\")\n", "print(f\"\\nActions les plus fréquentes:\")\n", "print(df_logs['action'].value_counts().head(10))\n", "print(f\"\\nLogs par programme:\")\n", "print(df_logs['programme'].value_counts())\n", "print(\"\\nAperçu des logs:\")\n", "display(df_logs.head(20))"]}, {"cell_type": "markdown", "id": "c36dbdda", "metadata": {}, "source": ["### 11. Affichage du DataFrame des Utilisateurs"]}, {"cell_type": "code", "execution_count": 29, "id": "d8f961db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATAFRAME DES UTILISATEURS ===\n", "Dimensions: (42, 6)\n", "\n", "Utilisateurs par fonction:\n", "fonction\n", "Commerciale KOTIBE              15\n", "Chargé d<PERSON>affair<PERSON>                7\n", "Responsable commercial           4\n", "CHEF DE VENTE                    3\n", "Directeur commercial             3\n", "Chargé d'affaire                 3\n", "Responsable Gestion Locative     2\n", "Commerciale INNOVIM              2\n", "Commerciale                      2\n", "Administrateur des ventes        1\n", "Name: count, dtype: int64\n", "\n", "Utilisateurs par programme:\n", "programme\n", "RESIDENCES LES JARDINS D'AHOUE    18\n", "BOREFLETS                         10\n", "LES RESIDENCES KOTIBE             10\n", "CALLISTO                           4\n", "Name: count, dtype: int64\n", "\n", "Liste des utilisateurs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>programme</th>\n", "      <th>firstname</th>\n", "      <th>lastname</th>\n", "      <th>fonction</th>\n", "      <th>phone</th>\n", "      <th>email</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BOREFLETS</td>\n", "      <td>ANOH</td>\n", "      <td>Rébecca</td>\n", "      <td>Responsable Gestion Locative</td>\n", "      <td>0554012121</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>BOREFLETS</td>\n", "      <td>ASSE</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Responsable commercial</td>\n", "      <td>0749975175</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>244</th>\n", "      <td>BOREFLETS</td>\n", "      <td>KEI</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON>'affair<PERSON></td>\n", "      <td>0554013232</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1408</th>\n", "      <td>BOREFLETS</td>\n", "      <td>DIDO</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON> d'affaire</td>\n", "      <td>0554011919</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1667</th>\n", "      <td>BOREFLETS</td>\n", "      <td>FOFANA</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON>'affair<PERSON></td>\n", "      <td>0566235353</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1937</th>\n", "      <td>BOREFLETS</td>\n", "      <td>TRAORE</td>\n", "      <td>Djeneba</td>\n", "      <td><PERSON><PERSON><PERSON>'affair<PERSON></td>\n", "      <td>0554013535</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2755</th>\n", "      <td>BOREFLETS</td>\n", "      <td>DOSSO</td>\n", "      <td><PERSON></td>\n", "      <td>Administrateur des ventes</td>\n", "      <td>0500995852</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3661</th>\n", "      <td>BOREFLETS</td>\n", "      <td>WILLIE</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Directeur commercial</td>\n", "      <td>0554014242</td>\n", "      <td>willie.boussa<PERSON>@kaydangroupe.com</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3979</th>\n", "      <td>BOREFLETS</td>\n", "      <td>BEAULAUD</td>\n", "      <td>Asse</td>\n", "      <td>CHEF DE VENTE</td>\n", "      <td>None</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4530</th>\n", "      <td>BOREFLETS</td>\n", "      <td>TRAORE</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Commerciale INNOVIM</td>\n", "      <td>0758981187</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5003</th>\n", "      <td>CALLISTO</td>\n", "      <td>DIDO</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON> d'affaire</td>\n", "      <td>0554011919</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5009</th>\n", "      <td>CALLISTO</td>\n", "      <td>WILLIE</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Directeur commercial</td>\n", "      <td>0554014242</td>\n", "      <td>willie.boussa<PERSON>@kaydangroupe.com</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5036</th>\n", "      <td>CALLISTO</td>\n", "      <td>FOFANA</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON>'affair<PERSON></td>\n", "      <td>0566235353</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5040</th>\n", "      <td>CALLISTO</td>\n", "      <td>BEAULAUD</td>\n", "      <td>Asse</td>\n", "      <td>CHEF DE VENTE</td>\n", "      <td>None</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5087</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>N'GUESSAN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Commerciale</td>\n", "      <td>0574394883</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5089</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>GBONON</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Responsable commercial</td>\n", "      <td>05 54 01 36 36</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5091</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>KOUAME</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>07 68 12 67 02</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5118</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>ASSI</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0709564670</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6169</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>KOUAKOU</td>\n", "      <td><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0759599887</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6283</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>DIGBA</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0585038989</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6291</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>KOFFI</td>\n", "      <td><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0140515117</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6293</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>KEMEY</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0584135151</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6310</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>OUATTARA</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0585260793</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6803</th>\n", "      <td>LES RESIDENCES KOTIBE</td>\n", "      <td>HOUPHOUET</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0595982605</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7044</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>ASSE</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Responsable commercial</td>\n", "      <td>0749975175</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7047</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>ASSI</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0709564670</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7049</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>GBONON</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Responsable commercial</td>\n", "      <td>05 54 01 36 36</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7060</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>ANOH</td>\n", "      <td>Rébecca</td>\n", "      <td>Responsable Gestion Locative</td>\n", "      <td>0554012121</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7103</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>DIDO</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON> d'affaire</td>\n", "      <td>0554011919</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7105</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>WILLIE</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Directeur commercial</td>\n", "      <td>0554014242</td>\n", "      <td>willie.boussa<PERSON>@kaydangroupe.com</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7107</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>KOUAKOU</td>\n", "      <td><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0759599887</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7108</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>KEMEY</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0584135151</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7159</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>FOFANA</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON>'affair<PERSON></td>\n", "      <td>0566235353</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7175</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>OUATTARA</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0585260793</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7177</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>DIGBA</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0585038989</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7214</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>KOFFI</td>\n", "      <td><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0140515117</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7258</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>TRAORE</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Commerciale INNOVIM</td>\n", "      <td>0758981187</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7512</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>N'GUESSAN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Commerciale</td>\n", "      <td>0574394883</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7839</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>HOUPHOUET</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Commerciale KOTIBE</td>\n", "      <td>0595982605</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7984</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>TRAORE</td>\n", "      <td>Djeneba</td>\n", "      <td><PERSON><PERSON><PERSON>'affair<PERSON></td>\n", "      <td>0554013535</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>8245</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>BEAULAUD</td>\n", "      <td>Asse</td>\n", "      <td>CHEF DE VENTE</td>\n", "      <td>None</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>8512</th>\n", "      <td>RESIDENCES LES JARDINS D'AHOUE</td>\n", "      <td>KEI</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON>'affair<PERSON></td>\n", "      <td>0554013232</td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           programme  firstname            lastname  \\\n", "0                          BOREFLETS       ANOH             Rébecca   \n", "12                         BOREFLETS       ASSE           N’Guessan   \n", "244                        BOREFLETS        KEI           Dominique   \n", "1408                       BOREFLETS       DIDO            Jean Luc   \n", "1667                       BOREFLETS     FOFANA              Zeinab   \n", "1937                       BOREFLETS     TRAORE             Djeneba   \n", "2755                       BOREFLETS      DOSSO             Mohamed   \n", "3661                       BOREFLETS     WILLIE           Boussamba   \n", "3979                       BOREFLETS   BEAULAUD                Asse   \n", "4530                       BOREFLETS     TRAORE           Abdoulaye   \n", "5003                        CALLISTO       DIDO            Jean <PERSON>   \n", "5009                        CALLISTO     WILLIE           Boussamba   \n", "5036                        CALLISTO     FOFANA              Zeinab   \n", "5040                        CALLISTO   BEAULAUD                Asse   \n", "5087           LES RESIDENCES KOTIBE  N'GUESSAN       Aya <PERSON>   \n", "5089           LES RESIDENCES KOTIBE     GBONON              Désiré   \n", "5091           LES RESIDENCES KOTIBE     KOUAME              Eliane   \n", "5118           LES RESIDENCES KOTIBE       ASSI            Marlaine   \n", "6169           LES RESIDENCES KOTIBE    KOUAKOU     <PERSON>   \n", "6283           LES RESIDENCES KOTIBE      DIGBA              Andréa   \n", "6291           LES RESIDENCES KOTIBE      KOFFI           Yao André   \n", "6293           LES RESIDENCES KOTIBE      KEMEY              Annick   \n", "6310           LES RESIDENCES KOTIBE   OUATTARA             Madjara   \n", "6803           LES RESIDENCES KOTIBE  HOUPHOUET  M'Mablaya Murielle   \n", "7044  RESIDENCES LES JARDINS D'AHOUE       ASSE           N’Guessan   \n", "7047  RESIDENCES LES JARDINS D'AHOUE       ASSI            Marlaine   \n", "7049  RESIDENCES LES JARDINS D'AHOUE     GBONON              Désiré   \n", "7060  RESIDENCES LES JARDINS D'AHOUE       ANOH             Rébecca   \n", "7103  RESIDENCES LES JARDINS D'AHOUE       DIDO            <PERSON>   \n", "7105  RESIDENCES LES JARDINS D'AHOUE     WILLIE           Boussamba   \n", "7107  RESIDENCES LES JARDINS D'AHOUE    KOUAKOU     <PERSON>   \n", "7108  RESIDENCES LES JARDINS D'AHOUE      KEMEY              Annick   \n", "7159  RESIDENCES LES JARDINS D'AHOUE     FOFANA              Zeinab   \n", "7175  RESIDENCES LES JARDINS D'AHOUE   OUATTARA             Madjara   \n", "7177  RESIDENCES LES JARDINS D'AHOUE      DIGBA              Andréa   \n", "7214  RESIDENCES LES JARDINS D'AHOUE      KOFFI           Yao André   \n", "7258  RESIDENCES LES JARDINS D'AHOUE     TRAORE           Abdoulaye   \n", "7512  RESIDENCES LES JARDINS D'AHOUE  N'GUESSAN       <PERSON><PERSON>   \n", "7839  RESIDENCES LES JARDINS D'AHOUE  HOUPHOUET  M'Mablaya Murielle   \n", "7984  RESIDENCES LES JARDINS D'AHOUE     TRAORE             Djeneba   \n", "8245  RESIDENCES LES JARDINS D'AHOUE   BEAULAUD                Asse   \n", "8512  RESIDENCES LES JARDINS D'AHOUE        KEI           Dominique   \n", "\n", "                          fonction           phone  \\\n", "0     Responsable Gestion Locative      0554012121   \n", "12          Responsable commercial      0749975175   \n", "244              Chargé d'affaires      0554013232   \n", "1408              <PERSON><PERSON><PERSON> d'affaire      0554011919   \n", "1667             <PERSON><PERSON><PERSON> d'affaires      0566235353   \n", "1937             Chargé d'affaires      0554013535   \n", "2755     Administrateur des ventes      0500995852   \n", "3661          Directeur commercial      0554014242   \n", "3979                 CHEF DE VENTE            None   \n", "4530           Commerciale INNOVIM      0758981187   \n", "5003              <PERSON>rgé d'affaire      0554011919   \n", "5009          Directeur commercial      0554014242   \n", "5036             <PERSON><PERSON><PERSON>'affair<PERSON>      0566235353   \n", "5040                 CHEF DE VENTE            None   \n", "5087                   Commerciale      0574394883   \n", "5089        Responsable commercial  05 54 01 36 36   \n", "5091            Commerciale KOTIBE  07 68 12 67 02   \n", "5118            Commerciale KOTIBE      0709564670   \n", "6169            Commerciale KOTIBE      0759599887   \n", "6283            Commerciale KOTIBE      0585038989   \n", "6291            Commerciale KOTIBE      0140515117   \n", "6293            Commerciale KOTIBE      0584135151   \n", "6310            Commerciale KOTIBE      0585260793   \n", "6803            Commerciale KOTIBE      0595982605   \n", "7044        Responsable commercial      0749975175   \n", "7047            Commerciale KOTIBE      0709564670   \n", "7049        Responsable commercial  05 54 01 36 36   \n", "7060  Responsable Gestion Locative      0554012121   \n", "7103              <PERSON><PERSON><PERSON> d'affaire      0554011919   \n", "7105          Directeur commercial      0554014242   \n", "7107            Commerciale KOTIBE      0759599887   \n", "7108            Commerciale KOTIBE      0584135151   \n", "7159             <PERSON><PERSON><PERSON> d'affair<PERSON>      0566235353   \n", "7175            Commerciale KOTIBE      0585260793   \n", "7177            Commerciale KOTIBE      0585038989   \n", "7214            Commerciale KOTIBE      0140515117   \n", "7258           Commerciale INNOVIM      0758981187   \n", "7512                   Commerciale      0574394883   \n", "7839            Commerciale KOTIBE      0595982605   \n", "7984             <PERSON><PERSON><PERSON> d'affaires      0554013535   \n", "8245                 CHEF DE VENTE            None   \n", "8512             <PERSON>rgé d'affaires      0554013232   \n", "\n", "                                    email  \n", "0           <EMAIL>  \n", "12         <EMAIL>  \n", "244        <EMAIL>  \n", "1408       <EMAIL>  \n", "1667       <EMAIL>  \n", "1937      <EMAIL>  \n", "2755       <EMAIL>  \n", "3661    <EMAIL>  \n", "3979                   <EMAIL>  \n", "4530    <EMAIL>  \n", "5003       <EMAIL>  \n", "5009    <EMAIL>  \n", "5036       <EMAIL>  \n", "5040                   <EMAIL>  \n", "5087  <EMAIL>  \n", "5089       <EMAIL>  \n", "5091       <EMAIL>  \n", "5118       <EMAIL>  \n", "6169  <EMAIL>  \n", "6283        <EMAIL>  \n", "6291         <EMAIL>  \n", "6293        <EMAIL>  \n", "6310    <EMAIL>  \n", "6803  <EMAIL>  \n", "7044       <EMAIL>  \n", "7047       <EMAIL>  \n", "7049       <EMAIL>  \n", "7060        <EMAIL>  \n", "7103       <EMAIL>  \n", "7105    <EMAIL>  \n", "7107  <EMAIL>  \n", "7108        <EMAIL>  \n", "7159       <EMAIL>  \n", "7175    <EMAIL>  \n", "7177        <EMAIL>  \n", "7214         <EMAIL>  \n", "7258    <EMAIL>  \n", "7512  <EMAIL>  \n", "7839  <EMAIL>  \n", "7984      <EMAIL>  \n", "8245                   <EMAIL>  \n", "8512       <EMAIL>  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"=== DATAFRAME DES UTILISATEURS ===\")\n", "print(f\"Dimensions: {df_users.shape}\")\n", "print(f\"\\nUtilisateurs par fonction:\")\n", "print(df_users['fonction'].value_counts())\n", "print(f\"\\nUtilisateurs par programme:\")\n", "print(df_users['programme'].value_counts())\n", "print(\"\\nListe des utilisateurs:\")\n", "display(df_users)"]}, {"cell_type": "markdown", "id": "04145548", "metadata": {}, "source": ["### 12. Affichage du DataFrame des Statistiques"]}, {"cell_type": "code", "execution_count": null, "id": "ca256f68", "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES STATISTIQUES ===\")\n", "print(f\"Dimensions: {df_stats.shape}\")\n", "print(f\"\\nTotal des ventes: {df_stats['ventes'].sum()}\")\n", "print(f\"Chiffre d'affaires total: {df_stats['chiffre_affaire'].sum():,.0f} FCFA\")\n", "print(f\"Recouvrement total: {df_stats['recouvrement'].sum():,.0f} FCFA\")\n", "print(f\"Reste à recouvrer total: {df_stats['reste_a_recouvrer'].sum():,.0f} FCFA\")\n", "print(\"\\nStatistiques par programme:\")\n", "display(df_stats)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}